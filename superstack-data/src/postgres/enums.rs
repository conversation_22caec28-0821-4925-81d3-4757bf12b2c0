use serde::{Deserialize, Serialize};

#[derive(
    <PERSON><PERSON>, <PERSON><PERSON>, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "chain_enum", rename_all = "lowercase")]
pub enum Chain {
    Solana,
    Hypercore,
    HyperEvm,
}

impl std::fmt::Display for Chain {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Chain::Solana => write!(f, "solana"),
            Chain::Hypercore => write!(f, "hypercore"),
            Chain::HyperEvm => write!(f, "hyperevm"),
        }
    }
}

impl std::str::FromStr for Chain {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "solana" => Chain::Solana,
            "hypercore" => Chain::Hypercore,
            "hyperevm" => Chain::HyperEvm,
            _ => return Err(anyhow::anyhow!("Invalid chain: {}", s)),
        })
    }
}

#[derive(
    Clone, Copy, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "dex_paid_enum", rename_all = "lowercase")]
pub enum DexPaid {
    Unknown,
    Paid,
    Unpaid,
}

impl std::fmt::Display for DexPaid {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DexPaid::Unknown => write!(f, "unknown"),
            DexPaid::Paid => write!(f, "paid"),
            DexPaid::Unpaid => write!(f, "unpaid"),
        }
    }
}

impl std::str::FromStr for DexPaid {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "unknown" => DexPaid::Unknown,
            "paid" => DexPaid::Paid,
            "unpaid" => DexPaid::Unpaid,
            _ => return Err(anyhow::anyhow!("Invalid dex paid: {}", s)),
        })
    }
}

#[derive(
    Clone, Copy, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "dex_enum", rename_all = "lowercase")]
pub enum Dex {
    Unknown,
    Pumpfun,
    Pumpswap,
    Meteora,
    Hypercore,
    HyperSwapV3,
}

impl std::fmt::Display for Dex {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Dex::Unknown => write!(f, "unknown"),
            Dex::Pumpfun => write!(f, "pumpfun"),
            Dex::Pumpswap => write!(f, "pumpswap"),
            Dex::Meteora => write!(f, "meteora"),
            Dex::Hypercore => write!(f, "hypercore"),
            Dex::HyperSwapV3 => write!(f, "hyperswapv3"),
        }
    }
}

impl std::str::FromStr for Dex {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "unknown" => Dex::Unknown,
            "pumpfun" => Dex::Pumpfun,
            "pumpswap" => Dex::Pumpswap,
            "meteora" => Dex::Meteora,
            "hypercore" => Dex::Hypercore,
            "hyperswapv3" => Dex::HyperSwapV3,
            _ => return Err(anyhow::anyhow!("Invalid dex: {}", s)),
        })
    }
}

#[derive(
    Clone, Copy, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "pool_type_enum", rename_all = "lowercase")]
pub enum PoolType {
    None,
    Dlmm,
}

impl std::fmt::Display for PoolType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PoolType::None => write!(f, "none"),
            PoolType::Dlmm => write!(f, "dlmm"),
        }
    }
}

impl std::str::FromStr for PoolType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "none" => PoolType::None,
            "dlmm" => PoolType::Dlmm,
            _ => return Err(anyhow::anyhow!("Invalid pool type: {}", s)),
        })
    }
}

#[derive(
    Clone, Copy, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "candle_interval_enum")]
pub enum CandleInterval {
    // seconds
    #[sqlx(rename = "1s")]
    S1,
    #[sqlx(rename = "5s")]
    S5,
    #[sqlx(rename = "15s")]
    S15,
    #[sqlx(rename = "30s")]
    S30,
    // minutes
    #[sqlx(rename = "1m")]
    M1,
    #[sqlx(rename = "5m")]
    M5,
    #[sqlx(rename = "15m")]
    M15,
    #[sqlx(rename = "30m")]
    M30,
    // hours
    #[sqlx(rename = "1h")]
    H1,
    #[sqlx(rename = "4h")]
    H4,
    #[sqlx(rename = "8h")]
    H8,
    #[sqlx(rename = "12h")]
    H12,
    #[sqlx(rename = "24h")]
    H24,
    // days
    #[sqlx(rename = "3d")]
    D3,
    #[sqlx(rename = "7d")]
    D7,
    #[sqlx(rename = "30d")]
    D30,
}

impl std::fmt::Display for CandleInterval {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

impl std::str::FromStr for CandleInterval {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "1s" => CandleInterval::S1,
            "5s" => CandleInterval::S5,
            "15s" => CandleInterval::S15,
            "30s" => CandleInterval::S30,
            "1m" => CandleInterval::M1,
            "5m" => CandleInterval::M5,
            "15m" => CandleInterval::M15,
            "30m" => CandleInterval::M30,
            "1h" => CandleInterval::H1,
            "4h" => CandleInterval::H4,
            "8h" => CandleInterval::H8,
            "12h" => CandleInterval::H12,
            "24h" => CandleInterval::H24,
            "3d" => CandleInterval::D3,
            "7d" => CandleInterval::D7,
            "30d" => CandleInterval::D30,
            _ => return Err(anyhow::anyhow!("Invalid candle interval: {}", s)),
        })
    }
}

impl CandleInterval {
    pub fn as_str(&self) -> &str {
        match self {
            CandleInterval::S1 => "1s",
            CandleInterval::S5 => "5s",
            CandleInterval::S15 => "15s",
            CandleInterval::S30 => "30s",
            CandleInterval::M1 => "1m",
            CandleInterval::M5 => "5m",
            CandleInterval::M15 => "15m",
            CandleInterval::M30 => "30m",
            CandleInterval::H1 => "1h",
            CandleInterval::H4 => "4h",
            CandleInterval::H8 => "8h",
            CandleInterval::H12 => "12h",
            CandleInterval::H24 => "24h",
            CandleInterval::D3 => "3d",
            CandleInterval::D7 => "7d",
            CandleInterval::D30 => "30d",
        }
    }

    pub fn as_seconds(&self) -> i64 {
        match self {
            CandleInterval::S1 => 1,
            CandleInterval::S5 => 5,
            CandleInterval::S15 => 15,
            CandleInterval::S30 => 30,
            CandleInterval::M1 => 60,
            CandleInterval::M5 => 5 * 60,
            CandleInterval::M15 => 15 * 60,
            CandleInterval::M30 => 30 * 60,
            CandleInterval::H1 => 60 * 60,
            CandleInterval::H4 => 4 * 60 * 60,
            CandleInterval::H8 => 8 * 60 * 60,
            CandleInterval::H12 => 12 * 60 * 60,
            CandleInterval::H24 => 24 * 60 * 60,
            CandleInterval::D3 => 3 * 24 * 60 * 60,
            CandleInterval::D7 => 7 * 24 * 60 * 60,
            CandleInterval::D30 => 30 * 24 * 60 * 60,
        }
    }
}

#[derive(
    Clone, Copy, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "maker_volume_type_enum", rename_all = "lowercase")]
pub enum MakerVolumeType {
    Plankton,
    Fish,
    Shrimp,
    Dolphin,
    Whale,
}

impl MakerVolumeType {
    pub fn from_volume(usd_volume: f64) -> Self {
        match usd_volume {
            usd_volume if usd_volume < 10.0 => Self::Plankton,
            usd_volume if usd_volume < 250.0 => Self::Fish,
            usd_volume if usd_volume < 1000.0 => Self::Shrimp,
            usd_volume if usd_volume < 10000.0 => Self::Dolphin,
            _ => Self::Whale,
        }
    }
}

impl std::fmt::Display for MakerVolumeType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MakerVolumeType::Plankton => write!(f, "plankton"),
            MakerVolumeType::Fish => write!(f, "fish"),
            MakerVolumeType::Shrimp => write!(f, "shrimp"),
            MakerVolumeType::Dolphin => write!(f, "dolphin"),
            MakerVolumeType::Whale => write!(f, "whale"),
        }
    }
}

impl std::str::FromStr for MakerVolumeType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "plankton" => MakerVolumeType::Plankton,
            "fish" => MakerVolumeType::Fish,
            "shrimp" => MakerVolumeType::Shrimp,
            "dolphin" => MakerVolumeType::Dolphin,
            "whale" => MakerVolumeType::Whale,
            _ => return Err(anyhow::anyhow!("Invalid maker volume type: {}", s)),
        })
    }
}

#[derive(
    Clone, Copy, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "maker_trade_type_enum", rename_all = "lowercase")]
pub enum MakerTradeType {
    None,
    Dev,
    Sniper,
    Bot,
    Insider,
}

impl std::fmt::Display for MakerTradeType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MakerTradeType::None => write!(f, "none"),
            MakerTradeType::Dev => write!(f, "dev"),
            MakerTradeType::Sniper => write!(f, "sniper"),
            MakerTradeType::Bot => write!(f, "bot"),
            MakerTradeType::Insider => write!(f, "insider"),
        }
    }
}

impl std::str::FromStr for MakerTradeType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "none" => MakerTradeType::None,
            "dev" => MakerTradeType::Dev,
            "sniper" => MakerTradeType::Sniper,
            "bot" => MakerTradeType::Bot,
            "insider" => MakerTradeType::Insider,
            _ => return Err(anyhow::anyhow!("Invalid maker trade type: {}", s)),
        })
    }
}

#[derive(
    Clone, Copy, Debug, Ord, Eq, PartialEq, PartialOrd, Hash, sqlx::Type, Deserialize, Serialize,
)]
#[sqlx(type_name = "perp_exchange_enum", rename_all = "lowercase")]
pub enum PerpExchange {
    Hyperliquid,
}

impl std::fmt::Display for PerpExchange {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PerpExchange::Hyperliquid => write!(f, "hyperliquid"),
        }
    }
}

impl std::str::FromStr for PerpExchange {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "hyperliquid" => PerpExchange::Hyperliquid,
            _ => return Err(anyhow::anyhow!("Invalid perp exchange: {}", s)),
        })
    }
}

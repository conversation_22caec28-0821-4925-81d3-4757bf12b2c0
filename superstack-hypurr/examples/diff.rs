use std::{collections::HashMap, fs::File};

use superstack_hypurr::types::state::State;

fn main() {
    let mut last_height = 0;
    let mut last_user_state: Option<UserStates> = None;
    loop {
        let path = "/home/<USER>/hlnode/hyperliquid_data/abci_state.rmp";

        let file = File::open(path).unwrap();

        let mut cursor = std::io::BufReader::new(file);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let state = State::from_rmpv_value(&value).unwrap();

        println!("height: {}", state.context.height);

        if last_height == state.context.height {
            println!(
                "height: {} is the same as last height: {}",
                state.context.height, last_height
            );
            continue;
        }

        last_height = state.context.height;

        let meta = &state.meta;
        let spot_meta = &state.spot_meta;
        let spot_books = &state.spot_books;

        // calculate user state
        let mut user_states = UserStates::new();
        for (user_addr, user_state) in state.user_states.iter() {
            let account_value = user_state.account_value(meta, spot_meta, spot_books);
            user_states.insert(user_addr.to_string(), UserState { account_value });
        }

        println!("new user_states: {}", user_states.len());

        if let Some(last_user_state) = last_user_state {
            let mut diff_count = 0;
            for (user_addr, user_state) in user_states.iter() {
                if user_state.account_value !=
                    last_user_state
                        .get(user_addr)
                        .and_then(|s| Some(s.account_value))
                        .unwrap_or(0)
                {
                    diff_count += 1;
                }
            }
            println!("diff count: {}", diff_count);
        }

        last_user_state = Some(user_states);
    }
}

#[derive(Debug, Clone, PartialEq, Eq)]
struct UserState {
    pub account_value: u128,
}

type UserStates = HashMap<String, UserState>;

[package]
name = "superstack-hypurr"
version.workspace = true
edition.workspace = true

[dependencies]
alloy = { workspace = true }
anyhow = { workspace = true }
bigdecimal = { workspace = true }
chrono = { workspace = true }
dotenv = { workspace = true }
hyperliquid_rust_sdk = "0.6.0"
moka = { workspace = true }
rmp-serde = "1.1"
rmpv = "1.0"
serde = { workspace = true }
serde_json = { workspace = true }
superstack-data = { path = "../superstack-data" }
tokio = { workspace = true }
tokio-util = "0.7.15"
tracing = { workspace = true }

[[bin]]
name = "hypurr"
path = "src/main.rs"

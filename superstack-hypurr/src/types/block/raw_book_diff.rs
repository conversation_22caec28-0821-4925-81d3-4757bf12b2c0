use serde::{Deserialize, Serialize};

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RawBookDiffEvent {
    pub user: String,
    pub oid: u64,
    pub coin: String,
    pub px: String,
    pub raw_book_diff: RawBookDiff,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum RawBookDiff {
    #[serde(rename = "new")]
    New { sz: String },
    #[serde(rename = "update")]
    Update {
        #[serde(rename = "origSz")]
        orig_sz: String,
        #[serde(rename = "newSz")]
        new_sz: String,
    },
    #[serde(rename = "remove")]
    Remove,
}

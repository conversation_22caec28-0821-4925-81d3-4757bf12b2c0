pub mod fill;
pub mod raw_book_diff;

use std::str::FromStr;

use serde::{de::DeserializeOwned, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct HyperBlock<T> {
    pub local_time: chrono::NaiveDateTime,
    pub block_time: chrono::NaiveDateTime,
    pub block_number: u64,

    pub events: Vec<T>,
}

impl<T: DeserializeOwned> FromStr for HyperBlock<T> {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let value = serde_json::from_str::<serde_json::Value>(s)?;

        let local_time = value
            .get("local_time")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid local_time"))?;
        let block_time = value
            .get("block_time")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid block_time"))?;
        let block_number = value
            .get("block_number")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid block_number"))?;
        let local_time = chrono::NaiveDateTime::parse_from_str(local_time, "%Y-%m-%dT%H:%M:%S%.f")
            .ok()
            .ok_or(anyhow::anyhow!("invalid local_time"))?;
        let block_time = chrono::NaiveDateTime::parse_from_str(block_time, "%Y-%m-%dT%H:%M:%S%.f")
            .ok()
            .ok_or(anyhow::anyhow!("invalid block_time"))?;

        let events = value
            .get("events")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid events"))?;
        let events = events
            .iter()
            .map(|v| serde_json::from_value::<T>(v.clone()))
            .collect::<Result<Vec<T>, _>>()?;

        Ok(Self { local_time, block_time, block_number, events })
    }
}

#[cfg(test)]
mod tests {
    use std::io::Read;

    use crate::types::block::raw_book_diff::RawBookDiffEvent;

    use super::*;

    #[test]
    fn test_parse_raw_book_diff() {
        let path = "src/types/samples/raw_book_diff.json";
        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = String::new();
        file.read_to_string(&mut buffer).unwrap();
        let book_diff = HyperBlock::<RawBookDiffEvent>::from_str(&buffer).unwrap();

        println!("{:?}", book_diff);
    }
}

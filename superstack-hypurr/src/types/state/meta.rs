use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};

use crate::utils::find_value;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Meta {
    /// universe
    pub universe: Vec<Universe>,
    /// pxs (px1, px2, last_update_time)
    pub pxs: Vec<(u64, u64, NaiveDateTime)>,
    // external_pxs: Vec<u64>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct Universe {
    pub margin_table_id: u64,
    pub name: String,
    pub sz_decimals: u64,
    pub only_isolated: Option<bool>,
}

impl Universe {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let margin_table_id = find_value(value, "marginTableId")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid margin_table_id"))?;
        let name = find_value(value, "name")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid name"))?
            .to_string();
        let sz_decimals = find_value(value, "szDecimals")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid sz_decimals"))?;
        let only_isolated = find_value(value, "onlyIsolated").and_then(|v| v.as_bool());

        Ok(Self { margin_table_id, name, sz_decimals, only_isolated })
    }
}

impl Meta {
    /// abci_state[exchange][perp_dexs][clearinghouse][meta]
    /// abci_state[exchange][perp_dexs][clearinghouse][oracle]
    pub fn from_rmpv_value(meta: &rmpv::Value, oracle: &rmpv::Value) -> anyhow::Result<Self> {
        let universes = find_value(meta, "universe")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid universes"))?;
        let pxs = find_value(oracle, "pxs")
            .and_then(|v| v.as_array())
            .ok_or(anyhow::anyhow!("invalid pxs"))?;

        let mut us = vec![];
        for u in universes {
            let universe = Universe::from_rmpv_value(u)?;
            us.push(universe);
        }

        let mut real_pxs = vec![];
        for p in pxs {
            let item = p.as_array().ok_or(anyhow::anyhow!("invalid p"))?;
            let px1 = find_value(&item[0], "px")
                .and_then(|v| v.as_array().and_then(|p| p[0].as_u64()))
                .ok_or(anyhow::anyhow!("invalid px1: {:?}", item))?;
            let px2 = find_value(&item[1], "px")
                .and_then(|v| v.as_array().and_then(|p| p[0].as_u64()))
                .ok_or(anyhow::anyhow!("invalid px2: {:?}", item))?;
            let last_update_time = find_value(&item[0], "last_update_time")
                .and_then(|v| v.as_str())
                .ok_or(anyhow::anyhow!("invalid last_update_time"))?;

            let last_update_time =
                chrono::NaiveDateTime::parse_from_str(last_update_time, "%Y-%m-%dT%H:%M:%S%.f")?;

            real_pxs.push((px1, px2, last_update_time));
        }

        Ok(Self { universe: us, pxs: real_pxs })
    }

    pub fn get_perp_idx(&self, perp_id: &str) -> Option<u64> {
        for (idx, universe) in self.universe.iter().enumerate() {
            if universe.name == perp_id {
                return Some(idx as u64);
            }
        }
        None
    }

    pub fn get_perp_name_by_idx(&self, idx: u64) -> Option<&str> {
        self.universe.get(idx as usize).map(|u| u.name.as_str())
    }

    /// px is in usdc, decimals = 6
    pub fn get_perp_px_by_idx(&self, idx: u64) -> Option<u64> {
        self.pxs.get(idx as usize).map(|px| (px.0 + px.1) / 2)
    }

    pub fn get_perp_sz_decimals_by_idx(&self, idx: u64) -> Option<u64> {
        self.universe.get(idx as usize).map(|u| u.sz_decimals)
    }
}

#[cfg(test)]
mod tests {
    use std::io::Read;

    use crate::{types::state::meta::Meta, utils::find_value};

    #[test]
    fn test_meta_from_rmpv_value() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";

        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let exchange = find_value(&value, "exchange").unwrap();
        let perp_dexs = &find_value(exchange, "perp_dexs").and_then(|v| v.as_array()).unwrap()[0];
        let clearinghouse = find_value(perp_dexs, "clearinghouse").unwrap();

        let meta = find_value(clearinghouse, "meta").unwrap();
        let oracle = find_value(clearinghouse, "oracle").unwrap();

        let meta = Meta::from_rmpv_value(meta, oracle).unwrap();

        let write_file = std::fs::File::create("src/types/samples/meta.json").unwrap();
        serde_json::to_writer_pretty(write_file, &meta).unwrap();
    }
}

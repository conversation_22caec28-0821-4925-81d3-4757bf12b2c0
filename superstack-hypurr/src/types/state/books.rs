use std::{collections::HashMap, str::FromStr};

use alloy::primitives::Address;
use serde::{Deserialize, Serialize};

use crate::utils::find_value;

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct Books(Vec<(u16, BookOrders)>);

impl Books {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let books_values = value.as_array().ok_or(anyhow::anyhow!("invalid books"))?;

        let mut books = Vec::new();
        for item in books_values {
            let asset_idx = find_value(item, "asset")
                .and_then(|v| v.as_u64())
                .ok_or(anyhow::anyhow!("invalid asset_idx"))? as u16;
            let book_orders_value =
                find_value(item, "book_orders").ok_or(anyhow::anyhow!("invalid book_orders"))?;
            let book_orders = BookOrders::from_rmpv_value(&book_orders_value)?;

            books.push((asset_idx, book_orders));
        }

        Ok(Self(books))
    }

    pub fn get_book_orders(&self, asset_idx: u16) -> Option<&BookOrders> {
        self.0.iter().find(|(idx, _)| *idx == asset_idx).map(|(_, book_orders)| book_orders)
    }

    pub fn get_book_orders_mut(&mut self, asset_idx: u16) -> Option<&mut BookOrders> {
        self.0.iter_mut().find(|(idx, _)| *idx == asset_idx).map(|(_, book_orders)| book_orders)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BookEntry {
    /// order id
    pub oid: u64,
    /// size of the order
    pub size: u64,
    /// original size of the order, !!!if not filled, this will be the same as size
    pub orig_size: u64,
    /// limit price of the order
    pub limit_px: u64,
    /// user who placed the order
    pub user: Address,
    /// direction of the order
    pub dir: BookDirection,
    /// time in force of the order
    pub tif: Option<String>,
    /// timestamp of the order
    pub ts: u64,
    /// child order id
    pub child_oid: Option<String>,
}

impl BookEntry {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let oid = find_value(value, "o")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid oid"))?;
        let size = find_value(value, "r")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid size"))?;
        let user = find_value(value, "u")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid user"))?;
        let user = Address::from_str(user).map_err(|e| anyhow::anyhow!("invalid user: {}", e))?;
        let tif = find_value(value, "tif").and_then(|v| v.as_str()).map(|v| v.to_string());

        let current = find_value(value, "c").ok_or(anyhow::anyhow!("invalid current"))?;

        let orig_size = find_value(current, "S")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid orig_size"))?;

        let limit_px = find_value(current, "l")
            .and_then(|v| v.as_array().and_then(|a| a.get(0).and_then(|v| v.as_u64())))
            .ok_or(anyhow::anyhow!("invalid limit"))?;

        let dir = find_value(current, "s")
            .and_then(|v| v.as_str())
            .ok_or(anyhow::anyhow!("invalid dir"))?;

        let dir = match dir {
            "A" => BookDirection::Ask,
            "B" => BookDirection::Bid,
            _ => anyhow::bail!("invalid dir: {}", dir),
        };

        let ts = find_value(current, "t")
            .and_then(|v| v.as_u64())
            .ok_or(anyhow::anyhow!("invalid ts"))?;

        let child_oid = find_value(current, "c").and_then(|v| v.as_str()).map(|v| v.to_string());

        Ok(Self { oid, size, orig_size, limit_px, user, dir, tif, ts, child_oid })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BookOrders {
    /// oid -> entry
    pub asks: HashMap<u64, BookEntry>,
    /// oid -> entry
    pub bids: HashMap<u64, BookEntry>,
}

impl BookOrders {
    pub fn from_rmpv_value(value: &rmpv::Value) -> anyhow::Result<Self> {
        let book_orders = value.as_map().ok_or(anyhow::anyhow!("invalid book_orders"))?;

        let mut asks = HashMap::new();
        let mut bids = HashMap::new();

        for (_, entry) in book_orders {
            let book_entry = BookEntry::from_rmpv_value(entry)?;
            let oid = book_entry.oid;
            let dir = book_entry.dir;

            match dir {
                BookDirection::Ask => {
                    asks.insert(oid, book_entry);
                }
                BookDirection::Bid => {
                    bids.insert(oid, book_entry);
                }
            }
        }

        Ok(Self { asks, bids })
    }

    pub fn get_book_entry(&self, oid: u64) -> Option<&BookEntry> {
        self.asks.get(&oid).or_else(|| self.bids.get(&oid))
    }

    pub fn get_book_entry_mut(&mut self, oid: u64) -> Option<&mut BookEntry> {
        self.asks.get_mut(&oid).or_else(|| self.bids.get_mut(&oid))
    }
}

#[derive(Debug, Copy, Clone, Serialize, Deserialize)]
pub enum BookDirection {
    Ask,
    Bid,
}

#[cfg(test)]
mod tests {
    use std::io::Read;

    use super::*;

    #[test]
    fn test_books_from_rmpv_value() {
        let path = "src/types/samples/hyperliquid_data/abci_state.rmp";
        //0xd2075830d96b6fe8d41d1620c38106c8c29e4b84
        let mut file = std::fs::File::open(path).unwrap();
        let mut buffer = Vec::new();
        file.read_to_end(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(&buffer);
        let value: rmpv::Value = rmpv::decode::read_value(&mut cursor).unwrap();

        let exchange = find_value(&value, "exchange").unwrap();
        let perp_dexs = &find_value(exchange, "perp_dexs").and_then(|v| v.as_array()).unwrap()[0];

        let books = find_value(perp_dexs, "books").unwrap();
        let books = Books::from_rmpv_value(&books).unwrap();

        let write_file = std::fs::File::create("src/types/samples/books.json").unwrap();
        serde_json::to_writer_pretty(write_file, &books).unwrap();
    }
}

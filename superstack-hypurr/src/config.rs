use dotenv::dotenv;
use std::sync::OnceLock;

#[derive(Debug)]
pub struct Config {
    pub hl_dir: String,
    pub hl_node: String,
    pub watch_interval_secs: u64,
    pub redis_url: String,
    pub pg_url: String,
    pub pg_max_connections: u32,
}

impl Config {
    pub fn get() -> &'static Config {
        static INSTANCE: OnceLock<Config> = OnceLock::new();

        INSTANCE.get_or_init(|| {
            dotenv().ok();

            let hl_dir = std::env::var("HL_DIR").expect("HL_DATA_DIR not set");
            let hl_node = std::env::var("HL_NODE").expect("HL_NODE not set");
            let watch_interval_secs =
                std::env::var("WATCH_INTERVAL_SECS").unwrap_or_else(|_| "1800".to_string());
            let redis_url = std::env::var("REDIS_URL").expect("REDIS_URL not set");
            let pg_url = std::env::var("PG_URL").expect("PG_URL not set");
            let pg_max_connections =
                std::env::var("PG_MAX_CONNECTIONS").unwrap_or_else(|_| "100".to_string());

            Config {
                hl_dir,
                hl_node,
                watch_interval_secs: watch_interval_secs.parse().unwrap(),
                redis_url,
                pg_url,
                pg_max_connections: pg_max_connections.parse().unwrap(),
            }
        })
    }
}

use std::{sync::Arc, time::Duration};

use anyhow::Error;
use hyperliquid_rust_sdk::{InfoClient, UserStateResponse};
use moka::future::Cache;
use superstack_data::{postgres::PostgresDatabase, redis::RedisClient};
use tokio::sync::{mpsc::Receiver, oneshot};

use crate::parsers::state::StateMessage;

pub struct StateProcessor {
    redis_client: RedisClient,
    // db_client: PostgresDatabase,
    rx: Receiver<(StateMessage, oneshot::Sender<()>)>,
    inner: Arc<HlStateInner>,
}

struct HlStateInner {
    hl_info_cli: InfoClient,
    hl_user_cache: Cache<String, Arc<UserStateResponse>>,
}

impl HlStateInner {
    async fn get_user_hl_state(&self, user_addr: &str) -> Option<Arc<UserStateResponse>> {
        let address = user_addr.parse().ok()?;
        match self
            .hl_user_cache
            .try_get_with(user_addr.to_owned(), async {
                let user_state = self.hl_info_cli.user_state(address).await?;
                Ok::<_, anyhow::Error>(Arc::new(user_state))
            })
            .await
        {
            Ok(user_state) => Some(user_state),
            Err(e) => {
                tracing::error!("error getting user state for {}: {}", user_addr, e);
                None
            }
        }
    }
}

impl StateProcessor {
    pub async fn new(
        redis_client: RedisClient,
        // db_client: PostgresDatabase,
        rx: Receiver<(StateMessage, oneshot::Sender<()>)>,
    ) -> Result<Self, Error> {
        // todo: support custome url
        let hl_info_cli =
            InfoClient::new(None, Some(hyperliquid_rust_sdk::BaseUrl::Localhost)).await?;
        let hl_user_cache =
            Cache::builder().max_capacity(2048).time_to_live(Duration::from_secs(60 * 3)).build();
        let inner = Arc::new(HlStateInner { hl_info_cli, hl_user_cache });
        Ok(Self { redis_client, rx, inner })
    }

    async fn process_state_msg(
        &self,
        StateMessage { perp_tops, perps, .. }: StateMessage,
    ) -> Result<(), anyhow::Error> {
        let now = chrono::Utc::now().timestamp_millis();
        // save user states to db
        // let pg = self.db_client.clone();
        // let user_states_task = tokio::spawn(async move {
        //     if let Err(e) = pg.insert_or_update_perp_user_states_chunked(&accts,
        // Some(1000)).await {         tracing::error!("Failed to save user states: {}", e);
        //     }
        // });

        // top users
        let redis = self.redis_client.clone();
        let inner = self.inner.clone();
        let top_users_task = tokio::spawn(async move {
            for (perp_name, tops) in perp_tops {
                let mut top_users_ok = vec![];
                for mut user_state in tops {
                    // try get liq px
                    if let Some(hl) = inner.get_user_hl_state(&user_state.user_addr).await {
                        let liq_px = hl
                            .asset_positions
                            .iter()
                            .find(|pos| &pos.position.coin == &perp_name)
                            .and_then(|pos| pos.position.liquidation_px.as_ref())
                            .and_then(|s| s.parse::<f64>().ok());

                        user_state.liq_px = liq_px;
                    };

                    user_state.timestamp_millis = now;
                    if let Err(e) = redis.set_perp_top_user(&user_state).await {
                        tracing::error!("Failed to write top users: {}", e);
                    } else {
                        top_users_ok.push(user_state.user_addr);
                    }
                }
                if let Err(e) = redis.set_perp_top_user_ids(&perp_name, &top_users_ok).await {
                    tracing::error!("Failed to write top users ids: {}", e);
                } else {
                    tracing::info!(
                        "Wrote {} top users for perp: {}",
                        top_users_ok.len(),
                        perp_name
                    );
                }
            }
        });

        // perps
        let redis = self.redis_client.clone();
        let perps_task = tokio::spawn(async move {
            let mut perps_ok = 0;
            for mut perp in perps.into_values() {
                perp.updated_at_millis = now;
                if let Err(e) = redis.set_perp_state(&perp).await {
                    tracing::error!("Failed to write perp state: {}", e);
                } else {
                    tracing::info!("Wrote perp state for token: {}", perp.perp_id);
                    perps_ok += 1;
                }
            }
            tracing::info!("Wrote {} perp states", perps_ok);
        });

        // wait for tasks to complete
        tokio::try_join!(top_users_task, perps_task)?;

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("perp state processor started");
        loop {
            match self.rx.recv().await {
                Some((state_msg, confirm)) => {
                    tracing::info!("processing state with {} tokens", state_msg.perps.len());

                    if let Err(e) = self.process_state_msg(state_msg).await {
                        tracing::error!("Error processing state: {}", e);
                    }

                    if confirm.send(()).is_err() {
                        tracing::error!("Failed to send confirmation");
                    }
                }
                None => break,
            }
        }
        tracing::info!("perp state processor stopped");
    }
}

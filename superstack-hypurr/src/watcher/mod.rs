// data_dir
//  - node_fills_by_block | node_order_status_by_block | node_twap_status_by_block
//      - hourly
//          - date(like 20250710)
//              - hour
//  - periodic_abci_states
//      - date(like 20250710)
//          - $block.rmp

use std::{cmp::Ordering, path::Path};
use tokio_util::sync::CancellationToken;

mod dir_watcher;
pub use dir_watcher::*;
mod file_watcher;
pub use file_watcher::*;

#[derive(Debug, Clone, Copy)]
pub enum FileCmp {
    ByNumLike,
}

impl FileCmp {
    pub fn cmp(&self, lhs: &Path, rhs: &Path) -> Ordering {
        let x = self.ord(lhs);
        let y = self.ord(rhs);

        match (x, y) {
            (Some(x), Some(y)) => x.cmp(&y),
            (Some(_), None) => Ordering::Greater,
            (None, Some(_)) => Ordering::Less,
            (None, None) => Ordering::Equal,
        }
    }

    pub fn ord(&self, f: &Path) -> Option<u64> {
        match self {
            FileCmp::ByNumLike => {
                f.file_stem().and_then(|s| s.to_str()).map(|s| s.parse::<u64>().unwrap_or_default())
            }
        }
    }
}

enum WatcherError {
    Io(std::io::Error),
    Channel,
}

pub struct Upstream {
    // node_fills: DirWatcher,
    pub abci_state: FileWatcher,
    cancel: CancellationToken,
}

impl Upstream {
    pub fn new(abci_state: FileWatcher, cancel: CancellationToken) -> Self {
        Self { abci_state, cancel }
    }

    /// Run both watchers concurrently
    pub async fn run(self) {
        let abci_state_task = tokio::spawn(async move {
            self.abci_state.run().await;
        });

        // Store abort handles
        let abci_state_abort = abci_state_task.abort_handle();

        // Use select! to handle cancellation
        tokio::select! {
            _ = self.cancel.cancelled() => {
                tracing::info!("upstream cancelled, aborting watchers");
            }
            Err(e) = abci_state_task => {
                tracing::error!("abci_state task failed: {e}");
            }
        }

        abci_state_abort.abort();
        tracing::info!("upstream stopped");
    }
}

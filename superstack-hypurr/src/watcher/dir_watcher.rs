use std::{fs, path::PathBuf, sync::Arc, time::Duration};
use tokio::sync::broadcast;

use super::{FileCmp, WatcherError};

#[derive(Clone)]
pub enum SearchPolicy {
    Recursive(FileCmp),
    NonRecursive(Vec<FileCmp>),
}

pub struct DirWatcher {
    inner: Arc<Inner>,
    tx: broadcast::Sender<PathBuf>,
}

#[derive(Clone)]
struct Inner {
    dir: PathBuf,
    watch_interval_secs: u64,
    search_policy: SearchPolicy,
    file_ext: String,
}

impl Inner {
    fn search_newest_file(&self) -> std::io::Result<Option<PathBuf>> {
        match &self.search_policy {
            SearchPolicy::Recursive(file_cmp) => {
                search_recursive(&self.dir, file_cmp, &self.file_ext)
            }
            SearchPolicy::NonRecursive(file_cmp_vec) => {
                search_non_recursive(&self.dir, file_cmp_vec, &self.file_ext)
            }
        }
    }
}

impl DirWatcher {
    pub fn new(
        dir: PathBuf,
        watch_interval_secs: u64,
        search_policy: SearchPolicy,
        file_ext: String,
        channel_capacity: usize,
    ) -> Self {
        let (tx, _) = broadcast::channel(channel_capacity);
        Self { inner: Arc::new(Inner { dir, watch_interval_secs, search_policy, file_ext }), tx }
    }

    /// Subscribe to file updates
    pub fn subscribe(&self) -> broadcast::Receiver<PathBuf> {
        self.tx.subscribe()
    }

    async fn step(&self) -> Result<(), WatcherError> {
        let inner = self.inner.clone();
        let res = tokio::task::spawn_blocking(move || inner.search_newest_file())
            .await
            .map_err(|_| WatcherError::Channel)?
            .map_err(WatcherError::Io)?;
        match res {
            Some(file) => {
                tracing::info!("newest file: {}", file.display());
                self.tx.send(file).map_err(|_| WatcherError::Channel)?;
            }
            None => {
                tracing::info!("no newest file found");
            }
        }
        Ok(())
    }

    pub async fn run(&self) {
        tracing::info!(
            "dir_watcher start: dir={}, file_ext={}",
            self.inner.dir.display(),
            self.inner.file_ext
        );
        loop {
            match self.step().await {
                Ok(_) => {}
                Err(WatcherError::Io(e)) => {
                    tracing::error!("dir_watcher error: {e}");
                }
                Err(WatcherError::Channel) => {
                    tracing::error!("channel disconnected");
                    break; // exit anyway
                }
            }
            tokio::time::sleep(Duration::from_secs(self.inner.watch_interval_secs)).await;
        }
    }
}

// apply `file_cmp` under `dir` recursively until newest expected file
fn search_recursive(
    dir: &PathBuf,
    file_cmp: &FileCmp,
    file_ext: &str,
) -> std::io::Result<Option<PathBuf>> {
    search_with_depth(dir, file_cmp, file_ext, None, 0)
}

fn search_non_recursive(
    dir: &PathBuf,
    file_cmp: &Vec<FileCmp>,
    file_ext: &str,
) -> std::io::Result<Option<PathBuf>> {
    if file_cmp.is_empty() {
        return Ok(None);
    }

    search_with_depth(dir, &file_cmp[0], file_ext, Some(file_cmp), 0)
}

fn search_with_depth(
    dir: &PathBuf,
    file_cmp: &FileCmp,
    file_ext: &str,
    file_cmp_vec: Option<&Vec<FileCmp>>,
    current_depth: usize,
) -> std::io::Result<Option<PathBuf>> {
    // Check depth limit for non-recursive search
    if let Some(cmp_vec) = file_cmp_vec {
        if current_depth >= cmp_vec.len() {
            return Ok(None);
        }
    }

    // Use the appropriate FileCmp for current depth
    let current_cmp =
        if let Some(cmp_vec) = file_cmp_vec { &cmp_vec[current_depth] } else { file_cmp };

    // Read the directory
    let entries = fs::read_dir(dir)?;

    // Collect all directories and files
    let mut subdirs = Vec::new();
    let mut files_with_ext = Vec::new();

    for entry in entries {
        let path = entry?.path();

        if path.is_dir() {
            // Check if directory name is numeric (YYYYMMDD format)
            if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                if name.chars().all(|c| c.is_ascii_digit()) {
                    subdirs.push(path);
                }
            }
        } else if path.is_file() {
            // Check if file has the expected extension
            if let Some(ext) = path.extension().and_then(|e| e.to_str()) {
                if ext == file_ext {
                    files_with_ext.push(path);
                }
            }
        }
    }

    // If we found files with the target extension in this directory,
    // find the one with the largest numeric stem
    if !files_with_ext.is_empty() {
        let newest_file = files_with_ext.into_iter().max_by(|a, b| current_cmp.cmp(a, b));
        return Ok(newest_file);
    }

    // If no files found, recursively search in subdirectories
    // Sort subdirectories by numeric name and search the largest one first
    if !subdirs.is_empty() {
        subdirs.sort_by(|a, b| current_cmp.cmp(a, b));

        // Search from the largest directory (most recent date)
        for subdir in subdirs.iter().rev() {
            if let Some(result) =
                search_with_depth(subdir, file_cmp, file_ext, file_cmp_vec, current_depth + 1)?
            {
                return Ok(Some(result));
            }
        }
    }

    Ok(None)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_search_recursive() {
        let dir = PathBuf::from("../../hl/data/periodic_abci_states");
        let file_cmp = FileCmp::ByNumLike;
        let file_ext = "rmp";
        let dir_watcher =
            DirWatcher::new(dir, 1, SearchPolicy::Recursive(file_cmp), file_ext.to_string(), 10);
        let result = dir_watcher.inner.search_newest_file();
        assert!(result.is_ok());
        let opt = result.unwrap();
        assert!(opt.is_some());
        println!("{:?}", opt.unwrap());
    }

    #[test]
    fn test_search_non_recursive() {
        let dir = PathBuf::from("../../hl/data/periodic_abci_states");
        let file_cmp = vec![FileCmp::ByNumLike; 2];
        let file_ext = "rmp";
        let dir_watcher =
            DirWatcher::new(dir, 1, SearchPolicy::NonRecursive(file_cmp), file_ext.to_string(), 10);
        let result = dir_watcher.inner.search_newest_file();
        assert!(result.is_ok());
        let opt = result.unwrap();
        assert!(opt.is_some());
        println!("{:?}", opt.unwrap());
    }
}

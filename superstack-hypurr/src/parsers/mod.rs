use crate::{cmd::HlNodeCmd, watcher::Upstream};
use tokio_util::sync::CancellationToken;

pub mod l4snapshot;
pub mod state;

use l4snapshot::SnapshotParser;
use state::StateParser;

pub struct Parsers {
    pub l4_snapshot: SnapshotParser,
    pub state: StateParser,
    cancel: CancellationToken,
}

impl Parsers {
    pub fn new(upstream: &Upstream, hl_node: HlNodeCmd, cancel: CancellationToken) -> Self {
        // Subscribe to upstream watchers based on parser needs
        let abci_state_rx = upstream.abci_state.subscribe();
        let l4_snapshot = SnapshotParser::new(hl_node, abci_state_rx);
        let abci_state_rx2 = upstream.abci_state.subscribe();
        let state = StateParser::new(abci_state_rx2);

        Self { l4_snapshot, state, cancel }
    }

    /// Run both parsers concurrently with internal subscription management
    pub async fn run(self) {
        let l4_snapshot_task = tokio::spawn(async move {
            self.l4_snapshot.run().await;
        });

        let state_task = tokio::spawn(async move {
            self.state.run().await;
        });

        // Store abort handles
        let l4_snapshot_abort = l4_snapshot_task.abort_handle();
        let state_abort = state_task.abort_handle();

        // Use select! to handle cancellation - centralized cancellation management per memory
        tokio::select! {
            _ = self.cancel.cancelled() => {
                tracing::info!("parsers cancelled, aborting tasks");
            }
            l4_result = l4_snapshot_task => {
                if let Err(e) = l4_result {
                    tracing::error!("l4_snapshot parser task failed: {e}");
                }
            }
            state_result = state_task => {
                if let Err(e) = state_result {
                    tracing::error!("state parser task failed: {e}");
                }
            }
        }

        l4_snapshot_abort.abort();
        state_abort.abort();
        tracing::info!("parsers stopped");
    }
}

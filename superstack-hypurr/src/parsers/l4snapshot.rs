use anyhow::Result;
use serde::Deserializer;
use std::{fs::File, io::BufReader, path::PathBuf};
use tokio::sync::{broadcast, mpsc, oneshot};

use crate::{
    cmd::HlNodeCmd,
    types::{L4BookStates, L4BookVisitor},
};

type AsyncTxConfirm = mpsc::Sender<(L4BookStates, oneshot::Sender<()>)>;
type AsyncRxConfirm = mpsc::Receiver<(L4BookStates, oneshot::Sender<()>)>;

pub struct SnapshotParser {
    hl_node: HlNodeCmd,
    abci_state_rx: broadcast::Receiver<PathBuf>,
    state_ch_async: Vec<(&'static str, AsyncTxConfirm)>,
}

impl SnapshotParser {
    pub fn new(hl_node: HlNodeCmd, abci_state_rx: broadcast::Receiver<PathBuf>) -> Self {
        Self { hl_node, abci_state_rx, state_ch_async: vec![] }
    }

    pub fn subscribe(&mut self, name: &'static str) -> AsyncRxConfirm {
        tracing::info!("registering async consumer {}", name);
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        self.state_ch_async.push((name, tx));
        rx
    }

    fn parse_l4_book(snap_json: &PathBuf) -> Result<L4BookStates> {
        let file = File::open(snap_json)?;
        let reader = BufReader::new(file);
        let mut de = serde_json::Deserializer::from_reader(reader);
        let tokens: L4BookStates = de.deserialize_seq(L4BookVisitor)?;
        Ok(tokens)
    }

    async fn process(&mut self, rmp: &PathBuf) -> Result<()> {
        // 1. convert using hl-node cmd
        let snap_json = {
            let output = {
                let fname: PathBuf = rmp.file_name().ok_or(anyhow::anyhow!("no file name"))?.into();
                PathBuf::from("/tmp").join(fname.with_extension("snap.json"))
            };
            tracing::info!("computing l4 snapshots for {}", rmp.display());
            self.hl_node.compute_l4_snapshots(rmp, &output).await?
        };

        // 2. parse as l4 book
        let l4_book = {
            let snap_json = snap_json.clone();
            tokio::task::spawn_blocking(move || Self::parse_l4_book(&snap_json)).await??
        };

        // 3. notify async consumers
        let mut remove = vec![];
        for (name, tx) in &self.state_ch_async {
            tracing::info!("notifying async consumer {}", name);
            let (ctx, crx) = oneshot::channel();
            if tx.send((l4_book.clone(), ctx)).await.is_err() {
                tracing::error!("async consumer {name} disconnected, removing");
                remove.push(name as &'static str);
            }
            if crx.await.is_err() {
                tracing::error!("async consumer {name} (confirm) disconnected, removing");
                remove.push(name as &'static str);
            }
        }
        self.state_ch_async.retain(|(name, _)| !remove.contains(name));

        // 4. remove snap.json
        let _ = tokio::fs::remove_file(&snap_json).await;

        Ok(())
    }

    pub async fn run(mut self) {
        tracing::info!("snapshot parser start");
        loop {
            match self.abci_state_rx.recv().await {
                Ok(rmp) => {
                    tracing::info!("processing rmp {}", rmp.display());
                    if let Err(e) = self.process(&rmp).await {
                        tracing::error!("error processing rmp: {e}");
                    } else {
                        tracing::info!("processed rmp");
                    }
                }
                Err(_) => {
                    tracing::error!("channel disconnected");
                    break;
                }
            }
        }
        tracing::info!("snapshot_parser stopped");
    }
}

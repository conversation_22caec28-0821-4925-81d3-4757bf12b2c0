use std::time::Duration;

use bigdecimal::{BigDecimal, ToPrimitive};
use cmc::async_api::CmcBuilder;
use superstack_data::{
    kafka::{topics::IndexerBlockMsg, KafkaProducer},
    postgres::{
        enums::{Chain, Dex, DexPaid, PoolType},
        indexer::{
            perp_state_series::PerpStateSeries, PerpState, PoolMetadata, PoolState, TokenMetadata,
        },
        PerpExchange, PostgresDatabase,
    },
    redis::RedisClient,
    utils::get_reqwest_client,
};
use tokio::task::{JoinHandle, JoinSet};

use crate::{
    config::Config,
    hypercore::{
        client::HypercoreClient,
        coinmarketcap::{get_token_info, search_token},
        info::spot::{SpotAssetCtx, SpotToken, SpotUniverse},
    },
};

pub mod client;
pub mod coingecko;
pub mod coinmarketcap;
pub mod info;
pub mod subscribe;
pub mod types;
pub mod utils;

const USDC_DECIMALS: u8 = 6;

#[derive(Debug, <PERSON>lone)]
pub struct HypercoreIndexer {
    client: HypercoreClient,
}

impl HypercoreIndexer {
    pub fn new(client: HypercoreClient) -> Self {
        Self { client }
    }

    pub async fn run(self) -> anyhow::Result<JoinHandle<()>> {
        tracing::info!("🕸️ Starting hypercore indexer");
        let self_ = self.clone();
        let handle_perp = tokio::spawn(async move {
            let mut perp_interval = tokio::time::interval(Duration::from_secs(5));
            let mut series_interval = tokio::time::interval(Duration::from_secs(60 * 5));
            loop {
                tokio::select! {
                    _ = perp_interval.tick() => {
                        if let Err(e) = self_.handle_perp().await {
                            tracing::error!("Error indexing hypercore: {}", e);
                        }
                    }
                    _ = series_interval.tick() => {
                        if let Err(e) = self_.handle_perp_series().await {
                            tracing::error!("Error indexing hypercore: {}", e);
                        }
                    }
                }
            }
        });

        let handle_spot = tokio::spawn(async move {
            loop {
                if let Err(e) = self.handle_spot().await {
                    tracing::error!("Error indexing hypercore: {}", e);
                }
                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        });

        Ok(tokio::spawn(async move {
            tokio::select! {
                _ = handle_perp => {
                    tracing::info!("🕸️ Hypercore perp indexer stopped");
                }
                _ = handle_spot => {
                    tracing::info!("🕸️ Hypercore spot indexer stopped");
                }
            }
        }))
    }

    pub async fn handle_perp(&self) -> anyhow::Result<()> {
        let db = PostgresDatabase::get_indexer_db().await;
        let redis_client = RedisClient::get_instance().await;
        let perps_and_ctxs = self.client.get_perps_and_ctxs().await?;
        let universe = perps_and_ctxs.meta.universe.clone();
        let universe_with_ctxs_iter =
            universe.into_iter().zip(perps_and_ctxs.asset_ctxs.into_iter());

        let update_timestamp_millis =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis()
                as i64;

        let cmc_client = CmcBuilder::new(&Config::get().cmc_api_key).pass(cmc::Pass::Id).build();

        let mut tasks = JoinSet::new();
        for (perp, ctx) in universe_with_ctxs_iter {
            if perp.is_delisted.unwrap_or(false) {
                continue;
            }

            let perp_id = perp.name.clone();

            let perp_info = match db.get_perp_info(PerpExchange::Hyperliquid, &perp_id).await? {
                Some(perp_info) => Some(perp_info),
                None => {
                    let coin = search_token(get_reqwest_client(), &perp_id).await.unwrap_or(None);
                    if let Some((coin, is_k_memecoin)) = coin {
                        let perp_info =
                            match get_token_info(&cmc_client, coin.id, &perp_id, is_k_memecoin)
                                .await
                            {
                                Ok(perp_info) => perp_info,
                                Err(e) => {
                                    tracing::error!("🕸️ Error getting perp info: {}", e);
                                    tokio::time::sleep(Duration::from_secs(10)).await;
                                    return Err(e);
                                }
                            };
                        tracing::info!("🕸️ Inserting perp info {:?}", perp_info);
                        db.insert_or_update_perp_info(&perp_info).await?;
                        Some(perp_info)
                    } else {
                        None
                    }
                }
            };

            tasks.spawn(async move {
                let total_sz = redis_client.get_perp_books_total_sz(&perp_id).await?.unwrap_or(0.0);
                let mut perp_state = PerpState {
                    perp_exchange: PerpExchange::Hyperliquid,
                    perp_id: perp_id.to_string(),
                    max_leverage: perp.max_leverage,
                    only_isolated: perp.only_isolated.unwrap_or(false),
                    sz_decimals: perp.sz_decimals,
                    funding: ctx.funding.parse::<f64>()?,
                    open_interest: ctx.open_interest.parse::<f64>()?,
                    premium: ctx.premium.clone(),
                    oracle_px: ctx.oracle_px.parse::<f64>()?,
                    impact_pxs: ctx.impact_pxs.map(|pxs| {
                        pxs.into_iter()
                            .map(|px| px.parse::<f64>())
                            .collect::<Result<Vec<_>, _>>()
                            .unwrap_or(vec![])
                    }),
                    day_base_vlm: ctx.day_base_vlm.parse::<f64>()?,
                    day_ntl_vlm: ctx.day_ntl_vlm.parse::<f64>()?,
                    mark_px: ctx.mark_px.parse::<f64>()?,
                    mid_px: ctx
                        .mid_px
                        .as_ref()
                        .map(|px| {
                            let px = px.parse::<f64>()?;
                            Ok::<_, anyhow::Error>(px)
                        })
                        .transpose()?,
                    prev_day_px: ctx.prev_day_px.parse::<f64>()?,
                    market_cap: 0.0,
                    liquidity: 0.0,
                    fdv: 0.0,
                    long_ntl: None,
                    short_ntl: None,
                    long_traders: None,
                    short_traders: None,
                    long_entry: None,
                    short_entry: None,
                    updated_at_millis: update_timestamp_millis,
                };

                if let Some(perp_info) = perp_info {
                    perp_state.liquidity = total_sz * perp_state.mark_px;
                    let circulating_supply =
                        perp_info.circulating_supply.to_string().parse::<f64>()?;
                    let total_supply = perp_info.total_supply.to_string().parse::<f64>()?;
                    perp_state.fdv = perp_state.mark_px * total_supply;
                    perp_state.market_cap = if circulating_supply > 0.0 {
                        perp_state.mark_px * circulating_supply
                    } else {
                        perp_state.mark_px * total_supply
                    };
                };

                Ok::<_, anyhow::Error>(perp_state)
            });
        }

        let perp_states = tasks.join_all().await.into_iter().collect::<Result<Vec<_>, _>>()?;
        tracing::info!("🕸️ Inserting {} perp states", perp_states.len());

        db.insert_or_update_perp_states(&perp_states).await?;

        Ok(())
    }

    pub async fn handle_perp_series(&self) -> anyhow::Result<()> {
        let db = PostgresDatabase::get_indexer_db().await;
        let redis_client = RedisClient::get_instance().await;
        let perps_and_ctxs = self.client.get_perps_and_ctxs().await?;
        let universe = perps_and_ctxs.meta.universe.clone();
        let universe_with_ctxs_iter =
            universe.into_iter().zip(perps_and_ctxs.asset_ctxs.into_iter());

        let now = chrono::Utc::now().timestamp_millis();
        let mut tasks = JoinSet::new();
        for (perp, ctx) in universe_with_ctxs_iter {
            if perp.is_delisted.unwrap_or(false) {
                continue;
            }

            let perp_id = perp.name;

            // perp state series
            tasks.spawn(async move {
                let bs = match redis_client.get_perp_state(&perp_id).await? {
                    Some(v) => v,
                    _ => {
                        tracing::warn!("No perp state series found for {}", perp_id);
                        return Ok(None);
                    }
                };

                let mark_px = ctx.mark_px.parse::<f64>()?;
                let perp_info = db.get_perp_info(PerpExchange::Hyperliquid, &perp_id).await?;
                let market_cap = perp_info.and_then(|info| {
                    let circulating_supply =
                        info.circulating_supply.to_string().parse::<f64>().ok()?;
                    let total_supply = info.total_supply.to_string().parse::<f64>().ok()?;
                    let market_cap = if circulating_supply > 0.0 {
                        mark_px * circulating_supply
                    } else {
                        mark_px * total_supply
                    };
                    Some(market_cap)
                });

                let series = PerpStateSeries {
                    perp_exchange: PerpExchange::Hyperliquid,
                    perp_id,
                    funding: ctx.funding.parse::<f64>()?,
                    open_interest: ctx.open_interest.parse::<f64>()?,
                    mark_px: ctx.mark_px.parse::<f64>()?,
                    market_cap: market_cap.unwrap_or_default(),
                    long_ntl: BigDecimal::from_biguint(bs.long_ntl.into(), USDC_DECIMALS as i64)
                        .to_f64()
                        .unwrap_or_default(),
                    short_ntl: BigDecimal::from_biguint(bs.short_ntl.into(), USDC_DECIMALS as i64)
                        .to_f64()
                        .unwrap_or_default(),
                    long_traders: bs.long_traders,
                    short_traders: bs.short_traders,
                    long_entry: bs.long_entry,
                    short_entry: bs.short_entry,
                    created_at_millis: now,
                };
                Ok::<_, anyhow::Error>(Some(series))
            });
        }
        let perp_state_series = tasks
            .join_all()
            .await
            .into_iter()
            .filter_map(Result::transpose)
            .collect::<Result<Vec<_>, _>>()?;
        if !perp_state_series.is_empty() {
            db.insert_perp_state_series(&perp_state_series).await?;
            tracing::info!("🕸️ Inserting {} perp state series", perp_state_series.len());
        }

        Ok(())
    }

    pub async fn handle_spot(&self) -> anyhow::Result<()> {
        let db = PostgresDatabase::get_indexer_db().await;
        let kafka_producer = KafkaProducer::get();
        let spots_and_ctxs = self.client.get_spots_and_ctxs().await?;
        let universe = spots_and_ctxs.meta.universe.clone();
        let block_number = self.client.get_latest_block_number().await?;
        tracing::info!("🕸️ Block number: {}", block_number);

        let mut tasks = JoinSet::new();

        for pool in universe {
            let update_timestamp_millis = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as i64;
            let tokens = (
                spots_and_ctxs.meta.tokens[pool.tokens[0]].clone(),
                spots_and_ctxs.meta.tokens[pool.tokens[1]].clone(),
            );
            let ctx = spots_and_ctxs.asset_ctxs[pool.index].clone();

            // token0 metadata
            tasks.spawn(async move {
                if db.get_token_metadata(Chain::Hypercore, &tokens.0.token_id).await?.is_none() {
                    let token_metadata = Self::construct_token_metadata(&tokens.0, &ctx)?;
                    db.insert_token_metadata(&token_metadata).await?;
                }

                // pool metadata
                if db.get_pool_metadata(Chain::Hypercore, &pool.name).await?.is_none() {
                    let pool_metadata =
                        Self::construct_pool_metadata(&pool, [&tokens.0, &tokens.1]).await?;
                    db.insert_pool_metadata(&pool_metadata).await?;
                }

                // pool state
                let mut pool_state = match db
                    .get_latest_pool_state_before_block_number(
                        Chain::Hypercore,
                        &pool.name,
                        block_number,
                    )
                    .await?
                {
                    Some(pool_state) => pool_state,
                    None => PoolState::new_hypercore_pool_state(
                        pool.name.clone(),
                        block_number,
                        update_timestamp_millis,
                    ),
                };

                let price = ctx.mark_px.parse::<f64>().unwrap();
                let circulating_supply = ctx.circulating_supply.parse::<f64>().unwrap();
                pool_state.block_number = block_number;
                pool_state.timestamp_millis = update_timestamp_millis;
                pool_state.price = price;
                pool_state.market_cap = price * circulating_supply;
                pool_state.liquidity = 0.0;
                Ok::<_, anyhow::Error>(pool_state)
            });
        }

        let pool_states = tasks.join_all().await.into_iter().collect::<Result<Vec<_>, _>>()?;

        tracing::info!("🕸️ Inserting {} pool states", pool_states.len());
        db.insert_pool_states(&pool_states).await?;
        let indexer_block_msg =
            IndexerBlockMsg::new(Chain::Hypercore, block_number, vec![], pool_states, vec![]);
        kafka_producer.send::<IndexerBlockMsg>(&indexer_block_msg).await?;

        Ok(())
    }

    pub fn construct_token_metadata(
        spot_token: &SpotToken,
        ctx: &SpotAssetCtx,
    ) -> anyhow::Result<TokenMetadata> {
        let circulating_supply = ctx.circulating_supply.parse::<f64>().unwrap();
        let multipier = 10_f64.powi(spot_token.wei_decimals as i32);
        let supply = (circulating_supply * multipier).trunc() as u128;

        // Fix image URL generation: ensure we use the symbol, not the address
        // If spot_token.name looks like an address (starts with 0x), we need to find the actual
        // symbol
        let symbol_for_image = if spot_token.name.starts_with("0x") {
            // If name contains an address, we have a data mapping issue
            // Try to extract symbol from the coin field in ctx, or use a fallback
            tracing::warn!(
                "Token name appears to be an address: {}, trying to use coin field",
                spot_token.name
            );

            // Use the coin field from context which should contain the symbol
            if !ctx.coin.is_empty() && !ctx.coin.starts_with("0x") {
                ctx.coin.clone()
            } else {
                // Last resort: use a generic fallback or try to derive from token_id
                tracing::error!(
                    "Cannot determine symbol for token {}, using fallback",
                    spot_token.token_id
                );
                "UNKNOWN".to_string()
            }
        } else {
            // Normal case: name should be the symbol (e.g., "HFUN")
            spot_token.name.clone()
        };

        Ok(TokenMetadata {
            chain: Chain::Hypercore,
            address: spot_token.token_id.clone(),
            name: spot_token.full_name.clone().unwrap_or_default(),
            symbol: spot_token.name.clone(),
            decimals: spot_token.wei_decimals,
            supply: supply.into(),
            description: None,
            image: Some(format!("https://app.hyperliquid.xyz/coins/{}_USDC.svg", symbol_for_image)),
            website: None,
            twitter: None,
            telegram: None,
            dex_paid: DexPaid::Unpaid,
            is_trench_token: false,
            create_dex: Dex::Hypercore,
            create_block_number: None,
            create_tx_hash: None,
            create_bonding_curve: None,
            create_dev: None,
            create_timestamp_millis: 0,
            migration_pool_address: None,
            migration_timestamp_millis: 0,
            update_timestamp_millis: 0,
            uri: None,
            seller_fee_basis_points: None,
            creators: None,
            primary_sale_happened: None,
            is_mutable: None,
            update_authority: None,
            mint_authority: None,
            freeze_authority: None,
            is_active: true,
            image_path: None,
        })
    }

    pub async fn construct_pool_metadata(
        pool: &SpotUniverse,
        spot_tokens: [&SpotToken; 2],
    ) -> anyhow::Result<PoolMetadata> {
        let timestamp_millis =
            std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis()
                as i64;
        Ok(PoolMetadata {
            chain: Chain::Hypercore,
            pool_address: pool.name.clone(),
            pair_label: format!("{}/{}", spot_tokens[0].name, spot_tokens[1].name),
            dex: Dex::Hypercore,
            pool_type: PoolType::None,
            create_timestamp_millis: timestamp_millis,
            update_timestamp_millis: timestamp_millis,
            token_address: spot_tokens[0].token_id.clone(),
            token_decimals: spot_tokens[0].wei_decimals,
            base_address: spot_tokens[1].token_id.clone(),
            base_decimals: spot_tokens[1].wei_decimals,
            is_token_first: true,
            is_active: true,
            bin_step: None,
        })
    }
}

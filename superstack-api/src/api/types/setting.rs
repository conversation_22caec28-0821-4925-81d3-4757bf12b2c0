use anyhow::Result;
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(<PERSON>lone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct SettingV1 {
    pub quick_trade: Option<QuickTrade>,
    pub wallet_swap_slippage: Option<WalletSwapSlippage>,
    pub spot_presets: Option<SpotPresetsV1>,
    pub perps_presets: Option<PerpsPresets>,
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct Setting {
    pub quick_trade: Option<QuickTrade>,
    pub wallet_swap_slippage: Option<WalletSwapSlippage>,
    pub spot_presets: Option<SpotPresets>,
    pub perps_presets: Option<PerpsPresets>,
}

impl Setting {
    pub fn update_with(&mut self, other: &Setting) {
        if let Some(other_quick_trade) = &other.quick_trade {
            if let Some(quick_trade) = &mut self.quick_trade {
                quick_trade.update_with(other_quick_trade);
            } else {
                self.quick_trade = other.quick_trade.clone();
            }
        }

        if let Some(other_wallet_swap_slippage) = &other.wallet_swap_slippage {
            if let Some(wallet_swap_slippage) = &mut self.wallet_swap_slippage {
                wallet_swap_slippage.update_with(other_wallet_swap_slippage);
            } else {
                self.wallet_swap_slippage = other.wallet_swap_slippage.clone();
            }
        }

        if let Some(other_spot_presets) = &other.spot_presets {
            if let Some(spot_presets) = &mut self.spot_presets {
                spot_presets.update_with(other_spot_presets);
            } else {
                self.spot_presets = other.spot_presets.clone();
            }
        }

        if let Some(other_perps_presets) = &other.perps_presets {
            if let Some(perps_presets) = &mut self.perps_presets {
                perps_presets.update_with(other_perps_presets);
            } else {
                self.perps_presets = other.perps_presets.clone();
            }
        }
    }

    pub fn from_json(value: Value) -> Result<Self> {
        let mut setting: Setting = serde_json::from_value(value.clone())?;

        if let Ok(setting_v1) = serde_json::from_value::<SettingV1>(value) {
            let quick_input_amount_v1 = setting_v1
                .spot_presets
                .as_ref()
                .and_then(|presets| presets.quick_input_amount.clone());
            let sell_percent_amount_v1 = setting_v1
                .spot_presets
                .as_ref()
                .and_then(|presets| presets.sell_percent_amount.clone());
            let p1_fields_v1 = setting_v1
                .spot_presets
                .as_ref()
                .and_then(|presets| presets.p1.as_ref().and_then(|p1| p1.fields.clone()));
            let p2_fields_v1 = setting_v1
                .spot_presets
                .as_ref()
                .and_then(|presets| presets.p2.as_ref().and_then(|p2| p2.fields.clone()));
            let p3_fields_v1 = setting_v1
                .spot_presets
                .as_ref()
                .and_then(|presets| presets.p3.as_ref().and_then(|p3| p3.fields.clone()));
            let p4_fields_v1 = setting_v1
                .spot_presets
                .as_ref()
                .and_then(|presets| presets.p4.as_ref().and_then(|p4| p4.fields.clone()));

            if let Some(spot_presets) = setting.spot_presets.as_mut() {
                if let Some(presets) = spot_presets.p1.as_mut() {
                    if presets.buy_input_amount.is_none() {
                        presets.buy_input_amount = quick_input_amount_v1.clone();
                    }
                    if presets.sell_percent_amount.is_none() {
                        presets.sell_percent_amount = sell_percent_amount_v1.clone();
                    }
                    if let Some(fields) = presets.fields.as_mut() {
                        if fields.buy.is_none() {
                            fields.buy = p1_fields_v1.clone();
                        }
                        if fields.sell.is_none() {
                            fields.sell = p1_fields_v1.clone();
                        }
                    }
                } else {
                    spot_presets.p1 = Some(Preset {
                        buy_input_amount: quick_input_amount_v1.clone(),
                        sell_percent_amount: sell_percent_amount_v1.clone(),
                        ..Default::default()
                    });
                }

                if let Some(presets) = spot_presets.p2.as_mut() {
                    if presets.buy_input_amount.is_none() {
                        presets.buy_input_amount = quick_input_amount_v1.clone();
                    }
                    if presets.sell_percent_amount.is_none() {
                        presets.sell_percent_amount = sell_percent_amount_v1.clone();
                    }
                    if let Some(fields) = presets.fields.as_mut() {
                        if fields.buy.is_none() {
                            fields.buy = p2_fields_v1.clone();
                        }
                        if fields.sell.is_none() {
                            fields.sell = p2_fields_v1.clone();
                        }
                    }
                } else {
                    spot_presets.p2 = Some(Preset {
                        buy_input_amount: quick_input_amount_v1.clone(),
                        sell_percent_amount: sell_percent_amount_v1.clone(),
                        ..Default::default()
                    });
                }

                if let Some(presets) = spot_presets.p3.as_mut() {
                    if presets.buy_input_amount.is_none() {
                        presets.buy_input_amount = quick_input_amount_v1.clone();
                    }
                    if presets.sell_percent_amount.is_none() {
                        presets.sell_percent_amount = sell_percent_amount_v1.clone();
                    }
                    if let Some(fields) = presets.fields.as_mut() {
                        if fields.buy.is_none() {
                            fields.buy = p3_fields_v1.clone();
                        }
                        if fields.sell.is_none() {
                            fields.sell = p3_fields_v1.clone();
                        }
                    }
                } else {
                    spot_presets.p3 = Some(Preset {
                        buy_input_amount: quick_input_amount_v1.clone(),
                        sell_percent_amount: sell_percent_amount_v1.clone(),
                        ..Default::default()
                    });
                }

                if let Some(presets) = spot_presets.p4.as_mut() {
                    if presets.buy_input_amount.is_none() {
                        presets.buy_input_amount = quick_input_amount_v1.clone();
                    }
                    if presets.sell_percent_amount.is_none() {
                        presets.sell_percent_amount = sell_percent_amount_v1.clone();
                    }
                    if let Some(fields) = presets.fields.as_mut() {
                        if fields.buy.is_none() {
                            fields.buy = p4_fields_v1.clone();
                        }
                        if fields.sell.is_none() {
                            fields.sell = p4_fields_v1.clone();
                        }
                    }
                } else {
                    spot_presets.p4 = Some(Preset {
                        buy_input_amount: quick_input_amount_v1.clone(),
                        sell_percent_amount: sell_percent_amount_v1.clone(),
                        ..Default::default()
                    });
                }
            }
        }
        Ok(setting)
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct QuickTrade {
    pub buy: Option<String>,
    pub sell: Option<String>,
}

impl QuickTrade {
    pub fn update_with(&mut self, other: &QuickTrade) {
        if other.buy.is_some() {
            self.buy = other.buy.clone();
        }
        if other.sell.is_some() {
            self.sell = other.sell.clone();
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct WalletSwapSlippage {
    #[serde(rename = "type")]
    pub slippage_type: Option<SlippageType>,
    pub value: Option<String>,
}

impl WalletSwapSlippage {
    pub fn update_with(&mut self, other: &WalletSwapSlippage) {
        if other.slippage_type.is_some() {
            self.slippage_type = other.slippage_type.clone();
        }
        if other.value.is_some() {
            self.value = other.value.clone();
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum SlippageType {
    Auto,
    Custom,
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct SpotPresetsV1 {
    pub quick_input_amount: Option<QuickInputAmount>,
    pub sell_percent_amount: Option<SellPercentAmount>,
    pub sell_use_percent: Option<bool>,
    pub current_preset: Option<String>,
    #[serde(rename = "P1")]
    pub p1: Option<PresetV1>,
    #[serde(rename = "P2")]
    pub p2: Option<PresetV1>,
    #[serde(rename = "P3")]
    pub p3: Option<PresetV1>,
    #[serde(rename = "P4")]
    pub p4: Option<PresetV1>,
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct SpotPresets {
    pub sell_use_percent: Option<bool>,
    pub current_preset: Option<String>,
    #[serde(rename = "P1")]
    pub p1: Option<Preset>,
    #[serde(rename = "P2")]
    pub p2: Option<Preset>,
    #[serde(rename = "P3")]
    pub p3: Option<Preset>,
    #[serde(rename = "P4")]
    pub p4: Option<Preset>,
}

impl SpotPresets {
    pub fn update_with(&mut self, other: &SpotPresets) {
        if other.sell_use_percent.is_some() {
            self.sell_use_percent = other.sell_use_percent;
        }

        if other.current_preset.is_some() {
            self.current_preset = other.current_preset.clone();
        }

        if let Some(other_p1) = &other.p1 {
            if let Some(p1) = &mut self.p1 {
                p1.update_with(other_p1);
            } else {
                self.p1 = other.p1.clone();
            }
        }

        if let Some(other_p2) = &other.p2 {
            if let Some(p2) = &mut self.p2 {
                p2.update_with(other_p2);
            } else {
                self.p2 = other.p2.clone();
            }
        }

        if let Some(other_p3) = &other.p3 {
            if let Some(p3) = &mut self.p3 {
                p3.update_with(other_p3);
            } else {
                self.p3 = other.p3.clone();
            }
        }

        if let Some(other_p4) = &other.p4 {
            if let Some(p4) = &mut self.p4 {
                p4.update_with(other_p4);
            } else {
                self.p4 = other.p4.clone();
            }
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct QuickInputAmount {
    pub currency: Option<String>,
    pub fields: Option<QuickInputFields>,
}

impl QuickInputAmount {
    pub fn update_with(&mut self, other: &QuickInputAmount) {
        if other.currency.is_some() {
            self.currency = other.currency.clone();
        }

        if let Some(other_fields) = &other.fields {
            if let Some(fields) = &mut self.fields {
                fields.update_with(other_fields);
            } else {
                self.fields = other.fields.clone();
            }
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct QuickInputFields {
    pub one: Option<String>,
    pub two: Option<String>,
    pub three: Option<String>,
    pub four: Option<String>,
}

impl QuickInputFields {
    pub fn update_with(&mut self, other: &QuickInputFields) {
        if other.one.is_some() {
            self.one = other.one.clone();
        }
        if other.two.is_some() {
            self.two = other.two.clone();
        }
        if other.three.is_some() {
            self.three = other.three.clone();
        }
        if other.four.is_some() {
            self.four = other.four.clone();
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct SellPercentAmount {
    pub one: Option<String>,
    pub two: Option<String>,
    pub three: Option<String>,
    pub four: Option<String>,
}

impl SellPercentAmount {
    pub fn update_with(&mut self, other: &SellPercentAmount) {
        if other.one.is_some() {
            self.one = other.one.clone();
        }
        if other.two.is_some() {
            self.two = other.two.clone();
        }
        if other.three.is_some() {
            self.three = other.three.clone();
        }
        if other.four.is_some() {
            self.four = other.four.clone();
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
pub struct PresetV1 {
    pub name: Option<String>,
    pub fields: Option<PresetFields>,
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct Preset {
    pub name: Option<String>,
    pub fields: Option<AllPresetFields>,
    pub buy_input_amount: Option<QuickInputAmount>,
    pub sell_percent_amount: Option<SellPercentAmount>,
    pub long_input_amount: Option<QuickInputAmount>,
    pub short_input_amount: Option<QuickInputAmount>,
    pub close_long_percent_amount: Option<SellPercentAmount>,
    pub close_short_percent_amount: Option<SellPercentAmount>,
}

impl Preset {
    pub fn update_with(&mut self, other: &Preset) {
        if other.name.is_some() {
            self.name = other.name.clone();
        }

        if let Some(other_fields) = &other.fields {
            if let Some(fields) = &mut self.fields {
                fields.update_with(other_fields);
            } else {
                self.fields = other.fields.clone();
            }
        }

        if let Some(other_buy_input_amount) = &other.buy_input_amount {
            if let Some(buy_input_amount) = &mut self.buy_input_amount {
                buy_input_amount.update_with(other_buy_input_amount);
            } else {
                self.buy_input_amount = other.buy_input_amount.clone();
            }
        }

        if let Some(other_sell_percent_amount) = &other.sell_percent_amount {
            if let Some(sell_percent_amount) = &mut self.sell_percent_amount {
                sell_percent_amount.update_with(other_sell_percent_amount);
            } else {
                self.sell_percent_amount = other.sell_percent_amount.clone();
            }
        }

        if let Some(other_long_input_amount) = &other.long_input_amount {
            if let Some(long_input_amount) = &mut self.long_input_amount {
                long_input_amount.update_with(other_long_input_amount);
            } else {
                self.long_input_amount = other.long_input_amount.clone();
            }
        }

        if let Some(other_short_input_amount) = &other.short_input_amount {
            if let Some(short_input_amount) = &mut self.short_input_amount {
                short_input_amount.update_with(other_short_input_amount);
            } else {
                self.short_input_amount = other.short_input_amount.clone();
            }
        }

        if let Some(other_close_long_percent_amount) = &other.close_long_percent_amount {
            if let Some(close_long_percent_amount) = &mut self.close_long_percent_amount {
                close_long_percent_amount.update_with(other_close_long_percent_amount);
            } else {
                self.close_long_percent_amount = other.close_long_percent_amount.clone();
            }
        }

        if let Some(other_close_short_percent_amount) = &other.close_short_percent_amount {
            if let Some(close_short_percent_amount) = &mut self.close_short_percent_amount {
                close_short_percent_amount.update_with(other_close_short_percent_amount);
            } else {
                self.close_short_percent_amount = other.close_short_percent_amount.clone();
            }
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct AllPresetFields {
    #[serde(rename = "Buy")]
    pub buy: Option<PresetFields>,
    #[serde(rename = "Sell")]
    pub sell: Option<PresetFields>,
}

impl AllPresetFields {
    pub fn update_with(&mut self, other: &AllPresetFields) {
        if let Some(other_buy) = &other.buy {
            if let Some(buy) = &mut self.buy {
                buy.update_with(other_buy);
            } else {
                self.buy = other.buy.clone();
            }
        }

        if let Some(other_sell) = &other.sell {
            if let Some(sell) = &mut self.sell {
                sell.update_with(other_sell);
            } else {
                self.sell = other.sell.clone();
            }
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PresetFields {
    pub slippage: Option<String>,
    pub priority: Option<String>,
    pub bribe: Option<String>,
    pub mev_reduction: Option<bool>,
}

impl PresetFields {
    pub fn update_with(&mut self, other: &PresetFields) {
        if other.slippage.is_some() {
            self.slippage = other.slippage.clone();
        }
        if other.priority.is_some() {
            self.priority = other.priority.clone();
        }
        if other.bribe.is_some() {
            self.bribe = other.bribe.clone();
        }
        if other.mev_reduction.is_some() {
            self.mev_reduction = other.mev_reduction;
        }
    }
}

#[derive(Clone, Debug, Deserialize, Serialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PerpsPresets {
    pub quick_input_amount: Option<QuickInputAmount>,
}

impl PerpsPresets {
    pub fn update_with(&mut self, other: &PerpsPresets) {
        if let Some(other_quick_input_amount) = &other.quick_input_amount {
            if let Some(quick_input_amount) = &mut self.quick_input_amount {
                quick_input_amount.update_with(other_quick_input_amount);
            } else {
                self.quick_input_amount = other.quick_input_amount.clone();
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_setting_update() {
        crate::utils::setup_tracing();

        let mut setting = Setting::default();
        tracing::info!("{}", serde_json::to_string(&setting).unwrap());

        let p_v1 = Some(Preset {
            buy_input_amount: Some(QuickInputAmount {
                currency: Some("v1".to_string()),
                fields: Some(QuickInputFields {
                    one: Some("v1".to_string()),
                    two: Some("v1".to_string()),
                    three: Some("v1".to_string()),
                    four: Some("v1".to_string()),
                }),
            }),
            sell_percent_amount: Some(SellPercentAmount {
                one: Some("v1".to_string()),
                two: Some("v1".to_string()),
                three: Some("v1".to_string()),
                four: Some("v1".to_string()),
            }),
            name: Some("v1".to_string()),
            fields: Some(AllPresetFields {
                buy: Some(PresetFields {
                    slippage: Some("v1".to_string()),
                    priority: Some("v1".to_string()),
                    bribe: Some("v1".to_string()),
                    mev_reduction: Some(false),
                }),
                sell: Some(PresetFields {
                    slippage: Some("v1".to_string()),
                    priority: Some("v1".to_string()),
                    bribe: Some("v1".to_string()),
                    mev_reduction: Some(false),
                }),
            }),
            long_input_amount: None,
            short_input_amount: None,
            close_long_percent_amount: Some(SellPercentAmount {
                one: Some("25".to_string()),
                two: Some("50".to_string()),
                three: Some("75".to_string()),
                four: Some("100".to_string()),
            }),
            close_short_percent_amount: Some(SellPercentAmount {
                one: Some("25".to_string()),
                two: Some("50".to_string()),
                three: Some("75".to_string()),
                four: Some("100".to_string()),
            }),
        });

        let new_setting_v1 = Setting {
            quick_trade: Some(QuickTrade {
                buy: Some("v1".to_string()),
                sell: Some("v1".to_string()),
            }),
            wallet_swap_slippage: Some(WalletSwapSlippage {
                slippage_type: Some(SlippageType::Auto),
                value: Some("v1".to_string()),
            }),
            spot_presets: Some(SpotPresets {
                sell_use_percent: Some(false),
                current_preset: Some("v1".to_string()),
                p1: p_v1.clone(),
                p2: p_v1.clone(),
                p3: p_v1.clone(),
                p4: p_v1.clone(),
            }),
            perps_presets: Some(PerpsPresets {
                quick_input_amount: Some(QuickInputAmount {
                    currency: Some("v1".to_string()),
                    fields: Some(QuickInputFields {
                        one: Some("v1".to_string()),
                        two: Some("v1".to_string()),
                        three: Some("v1".to_string()),
                        four: Some("v1".to_string()),
                    }),
                }),
            }),
        };

        setting.update_with(&new_setting_v1);
        tracing::info!("{}", serde_json::to_string(&setting).unwrap());

        let p_v2 = Some(Preset {
            buy_input_amount: Some(QuickInputAmount {
                currency: Some("v2".to_string()),
                fields: Some(QuickInputFields {
                    one: Some("v2".to_string()),
                    two: Some("v2".to_string()),
                    three: Some("v2".to_string()),
                    four: Some("v2".to_string()),
                }),
            }),
            sell_percent_amount: Some(SellPercentAmount {
                one: Some("v2".to_string()),
                two: Some("v2".to_string()),
                three: Some("v2".to_string()),
                four: Some("v2".to_string()),
            }),
            name: Some("v2".to_string()),
            fields: Some(AllPresetFields {
                buy: Some(PresetFields {
                    slippage: Some("v2".to_string()),
                    priority: Some("v2".to_string()),
                    bribe: Some("v2".to_string()),
                    mev_reduction: Some(true),
                }),
                sell: Some(PresetFields {
                    slippage: Some("v2".to_string()),
                    priority: Some("v2".to_string()),
                    bribe: Some("v2".to_string()),
                    mev_reduction: Some(true),
                }),
            }),
            long_input_amount: None,
            short_input_amount: None,
            close_long_percent_amount: Some(SellPercentAmount {
                one: Some("25".to_string()),
                two: Some("50".to_string()),
                three: Some("75".to_string()),
                four: Some("100".to_string()),
            }),
            close_short_percent_amount: Some(SellPercentAmount {
                one: Some("25".to_string()),
                two: Some("50".to_string()),
                three: Some("75".to_string()),
                four: Some("100".to_string()),
            }),
        });

        let new_setting_v2 = Setting {
            quick_trade: Some(QuickTrade {
                buy: Some("v2".to_string()),
                sell: Some("v2".to_string()),
            }),
            wallet_swap_slippage: Some(WalletSwapSlippage {
                slippage_type: Some(SlippageType::Custom),
                value: Some("v2".to_string()),
            }),
            spot_presets: Some(SpotPresets {
                sell_use_percent: Some(true),
                current_preset: Some("v2".to_string()),
                p1: p_v2.clone(),
                p2: p_v2.clone(),
                p3: p_v2.clone(),
                p4: p_v2.clone(),
            }),
            perps_presets: Some(PerpsPresets {
                quick_input_amount: Some(QuickInputAmount {
                    currency: Some("v2".to_string()),
                    fields: Some(QuickInputFields {
                        one: Some("v2".to_string()),
                        two: Some("v2".to_string()),
                        three: Some("v2".to_string()),
                        four: Some("v2".to_string()),
                    }),
                }),
            }),
        };

        setting.update_with(&new_setting_v2);
        tracing::info!("{}", serde_json::to_string(&setting).unwrap());
    }

    #[test]
    fn test_migrate_old_setting() {
        crate::utils::setup_tracing();

        let old_setting_json_str = r#"
{"quickTrade": {"buy": "0.1", "sell": "100"}, "spotPresets": {"P1": {"name": "P1", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "5", "mev_reduction": null}}, "P2": {"name": "P2", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "1", "mev_reduction": null}}, "P3": {"name": "P3", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P4": {"name": "P4", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "currentPreset": "P2", "sellUsePercent": true, "quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}, "sellPercentAmount": {"one": "25", "two": "50", "four": "100", "three": "75"}}, "perpsPresets": {"quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}}, "walletSwapSlippage": {"type": "auto", "value": "1"}}
        "#;
        let json_value: Value = serde_json::from_str(old_setting_json_str).unwrap();
        let setting: Setting = Setting::from_json(json_value).unwrap();
        tracing::info!("Migrated setting: {}", serde_json::to_string(&setting).unwrap());

        let old_setting_json_str = r#"
{"quickTrade": {"buy": "0.1", "sell": "100"}, "spotPresets": {"P1": {"name": "P1", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "4", "mev_reduction": null}}, "P2": {"name": "P2", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "10", "mev_reduction": null}}, "P3": {"name": "P3", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P4": {"name": "P4", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "currentPreset": "P1", "sellUsePercent": true, "quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}, "sellPercentAmount": {"one": "25", "two": "50", "four": "100", "three": "75"}}, "perpsPresets": {"quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}}, "walletSwapSlippage": {"type": "auto", "value": "1"}}
        "#;
        let json_value: Value = serde_json::from_str(old_setting_json_str).unwrap();
        let setting: Setting = Setting::from_json(json_value).unwrap();
        tracing::info!("Migrated setting: {}", serde_json::to_string(&setting).unwrap());

        let old_setting_json_str = r#"
{"quickTrade": {"buy": "0.1", "sell": "100"}, "spotPresets": {"P1": {"name": "P1", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "44", "mev_reduction": null}}, "P2": {"name": "test", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P3": {"name": "P3", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P4": {"name": "P4", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "currentPreset": "P4", "sellUsePercent": true, "quickInputAmount": {"fields": {"one": "100", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}, "sellPercentAmount": {"one": "25", "two": "50", "four": "100", "three": "75"}}, "perpsPresets": {"quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}}, "walletSwapSlippage": {"type": "auto", "value": "1"}}
        "#;
        let json_value: Value = serde_json::from_str(old_setting_json_str).unwrap();
        let setting: Setting = Setting::from_json(json_value).unwrap();
        tracing::info!("Migrated setting: {}", serde_json::to_string(&setting).unwrap());

        let old_setting_json_str = r#"
{"quickTrade": {"buy": "0.1", "sell": "100"}, "spotPresets": {"P1": {"name": "normal", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "5", "mev_reduction": null}}, "P2": {"name": "rage", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "10", "mev_reduction": null}}, "P3": {"name": "P3", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P4": {"name": "P4", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "currentPreset": "P1", "sellUsePercent": true, "quickInputAmount": {"fields": {"one": "1", "two": "1230", "four": "23", "three": "34343"}, "currency": "USD"}, "sellPercentAmount": {"one": "25", "two": "50", "four": "100", "three": "75"}}, "perpsPresets": {"quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}}, "walletSwapSlippage": {"type": "auto", "value": "1"}}
        "#;
        let json_value: Value = serde_json::from_str(old_setting_json_str).unwrap();
        let setting: Setting = Setting::from_json(json_value).unwrap();
        tracing::info!("Migrated setting: {}", serde_json::to_string(&setting).unwrap());

        let old_setting_json_str = r#"
{"quickTrade": {"buy": "0.1", "sell": "100"}, "spotPresets": {"P1": {"name": "P1", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P2": {"name": "P2", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P3": {"name": "P3", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "P4": {"name": "P4", "fields": {"bribe": "0.001", "priority": "0.001", "slippage": "20", "mev_reduction": null}}, "currentPreset": "P1", "sellUsePercent": true, "quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}, "sellPercentAmount": {"one": "25", "two": "50", "four": "100", "three": "75"}}, "perpsPresets": {"quickInputAmount": {"fields": {"one": "1", "two": "10", "four": "500", "three": "100"}, "currency": "USD"}}, "walletSwapSlippage": {"type": "auto", "value": "1"}}
        "#;
        let json_value: Value = serde_json::from_str(old_setting_json_str).unwrap();
        let setting: Setting = Setting::from_json(json_value).unwrap();
        tracing::info!("Migrated setting: {}", serde_json::to_string(&setting).unwrap());
    }
}

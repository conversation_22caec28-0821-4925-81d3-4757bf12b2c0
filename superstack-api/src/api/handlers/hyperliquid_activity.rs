use axum::{extract::State, http::StatusCode, Json};
use rust_decimal::prelude::FromPrimitive;
use std::time::Instant;

use super::ErrorResponse;
use crate::{
    hyperliquid_parser::HyperliquidParser,
    models::{
        hyperliquid_activity::{RecordActivityRequest, RecordActivityResponse},
        ActivityType, DbWalletActivity, StorageState,
    },
};

/// Maximum number of activities allowed in a single request
const MAX_ACTIVITIES_PER_REQUEST: usize = 1000;

/// Maximum request size in bytes (1MB)
const MAX_REQUEST_SIZE_BYTES: usize = 1024 * 1024;

/// Handler for POST /api/record/activity
/// Records Hyperliquid wallet activity data from frontend
pub async fn record_hyperliquid_activity(
    State(state): State<StorageState>,
    headers: axum::http::HeaderMap,
    Json(request): <PERSON><PERSON><RecordActivityRequest>,
) -> Result<Json<RecordActivityResponse>, (StatusCode, Json<ErrorResponse>)> {
    let start_time = Instant::now();

    // Extract SOL wallet address from header for referral rewards
    let sol_wallet_address =
        headers.get("X-Wallet-Address").and_then(|h| h.to_str().ok()).map(|s| s.to_string());

    tracing::info!(
        "Received HyperEvm activity record request for wallet: {}, sol_wallet: {:?}, fills: {} [DISABLED], ledger_updates: {}, perp_orders: {}, order_updates: {}, twap_fills: {}, deposits: {}, withdraws: {}",
        request.wallet_address,
        sol_wallet_address,
        request.activities.user_fills.as_ref().map_or(0, |f| f.len()),
        request.activities.user_non_funding_ledger_updates.as_ref().map_or(0, |u| u.len()),
        if request.activities.perps_order.is_some() { 1 } else { 0 },
        request.activities.order_updates.as_ref().map_or(0, |s| s.len()),
        if request.activities.twap_fills_history.is_some() { 1 } else { 0 },
        request.activities.deposits.as_ref().map_or(0, |d| d.len()),
        request.activities.withdraws.as_ref().map_or(0, |w| w.len())
    );

    // Enhanced request validation with duplicate filtering
    let (validation_warnings, filtered_request) = match validate_request_enhanced(&request) {
        Ok((warnings, filtered_req)) => (warnings, filtered_req),
        Err(e) => {
            tracing::warn!(
                "Enhanced validation failed for wallet {}: {}",
                request.wallet_address,
                e
            );
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new(&format!("Validation failed: {}", e))),
            ));
        }
    };

    // Basic request validation on filtered request
    if let Err(e) = HyperliquidParser::validate_request(&filtered_request) {
        tracing::warn!(
            "Basic validation failed for wallet {}: {}",
            filtered_request.wallet_address,
            e
        );
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::new(&format!("Invalid request: {}", e))),
        ));
    }

    // Check wallet authorization
    match check_wallet_authorization(&state, &filtered_request.wallet_address).await {
        Ok(false) => {
            tracing::warn!(
                "Unauthorized wallet attempted to submit activities: {}",
                filtered_request.wallet_address
            );
            return Err((
                StatusCode::FORBIDDEN,
                Json(ErrorResponse::new("Wallet not authorized to submit activities")),
            ));
        }
        Err(e) => {
            tracing::error!("Failed to check wallet authorization: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Authorization check failed")),
            ));
        }
        Ok(true) => {} // Continue processing
    }

    // Parse activities from the filtered request
    let activities = match HyperliquidParser::parse_activities(&filtered_request) {
        Ok(activities) => activities,
        Err(e) => {
            tracing::error!(
                "Failed to parse activities for wallet {}: {}",
                filtered_request.wallet_address,
                e
            );
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::new("Failed to parse activities")),
            ));
        }
    };

    if activities.is_empty() {
        tracing::warn!(
            "No valid activities to process for wallet {}",
            filtered_request.wallet_address
        );
        let response = RecordActivityResponse {
            success: true,
            message: if validation_warnings.is_empty() {
                "No valid activities to process".to_string()
            } else {
                format!(
                    "No valid activities to process. Warnings: {}",
                    validation_warnings.join("; ")
                )
            },
            processed_count: 0,
            failed_count: 0,
            errors: if validation_warnings.is_empty() { None } else { Some(validation_warnings) },
        };

        // Log completion even for empty results
        let processing_time = start_time.elapsed();
        tracing::info!(
            "Completed HyperEvm activity processing for wallet {} in {:?}: no activities to process",
            filtered_request.wallet_address,
            processing_time
        );

        return Ok(Json(response));
    }

    // Remove duplicates
    let deduplicated_activities = HyperliquidParser::deduplicate_activities(activities);
    let processed_count = deduplicated_activities.len();

    // Get activity statistics for logging
    let stats = HyperliquidParser::get_activity_stats(&deduplicated_activities);
    tracing::info!(
        "Processing {} activities for wallet {}: {:?}",
        processed_count,
        filtered_request.wallet_address,
        stats
    );

    // Log validation warnings if any
    if !validation_warnings.is_empty() {
        tracing::info!(
            "Validation warnings for wallet {}: {}",
            filtered_request.wallet_address,
            validation_warnings.join("; ")
        );
    }

    // Log activity statistics before storage
    log_activity_statistics(
        &filtered_request.wallet_address,
        &deduplicated_activities,
        start_time.elapsed(),
    );

    // Use universal update framework for all activities
    let deduplicated_activities_clone = deduplicated_activities.clone();
    match state.api_db.update_with_universal_framework(deduplicated_activities).await {
        Ok(mut update_result) => {
            update_result.finalize_stats();

            // Execute database operations based on update result
            let mut failed_count = 0;
            let mut errors = Vec::new();
            let mut successful_activities = Vec::new();

            // Insert new records
            for activity in update_result.new_records {
                let tx_signature = activity.tx_signature.clone();
                match state.insert_wallet_activity(activity.clone()).await {
                    Ok(_) => {
                        successful_activities.push(activity);
                        tracing::debug!("Inserted new activity: {}", tx_signature);
                    }
                    Err(e) => {
                        failed_count += 1;
                        let error_msg = format!(
                            "Failed to insert new activity {}: {}",
                            activity.tx_signature, e
                        );
                        errors.push(error_msg.clone());
                        tracing::error!("{}", error_msg);
                    }
                }
            }

            // Update existing records
            for activity in update_result.updated_records {
                // Check if this is a userfill_aggregated update by looking at merge_history
                let is_userfill_update = activity
                    .metadata
                    .as_ref()
                    .and_then(|m| m.get("merge_history"))
                    .and_then(|h| h.get("new_source"))
                    .and_then(|s| s.as_str())
                    .map(|s| s == "userfill_aggregated")
                    .unwrap_or(false);

                let update_result = if is_userfill_update {
                    // Extract oid for userfill_aggregated updates
                    if let Some(oid) = activity
                        .metadata
                        .as_ref()
                        .and_then(|m| m.get("oid"))
                        .and_then(|o| o.as_i64())
                    {
                        state.api_db.update_wallet_activity_by_oid(&activity, oid).await
                    } else {
                        state.api_db.update_wallet_activity_by_signature(&activity).await
                    }
                } else {
                    state.api_db.update_wallet_activity_by_signature(&activity).await
                };

                match update_result {
                    Ok(_) => {
                        successful_activities.push(activity.clone());
                        tracing::debug!("Updated existing activity: {}", activity.tx_signature);
                    }
                    Err(e) => {
                        failed_count += 1;
                        let error_msg =
                            format!("Failed to update activity {}: {}", activity.tx_signature, e);
                        errors.push(error_msg.clone());
                        tracing::error!("{}", error_msg);
                    }
                }
            }

            // Log skipped records
            for activity in &update_result.skipped_records {
                tracing::debug!(
                    "Skipped activity due to priority rules: {}",
                    activity.tx_signature
                );
            }

            let success_count = successful_activities.len();
            let processed_count =
                success_count + failed_count + update_result.skipped_records.len();

            if failed_count > 0 {
                tracing::warn!(
                    "Processed {}/{} activities for wallet {}: {} inserted, {} updated, {} skipped, {} failed",
                    success_count,
                    processed_count,
                    filtered_request.wallet_address,
                    update_result.stats.new_count,
                    update_result.stats.updated_count,
                    update_result.stats.skipped_count,
                    failed_count
                );
            } else {
                tracing::info!(
                    "Successfully processed {} activities for wallet {}: {} inserted, {} updated, {} skipped",
                    success_count,
                    filtered_request.wallet_address,
                    update_result.stats.new_count,
                    update_result.stats.updated_count,
                    update_result.stats.skipped_count
                );
            }

            // Return success response with detailed stats
            let response = RecordActivityResponse {
                success: true,
                message: format!(
                    "Processed {} activities: {} new, {} updated, {} skipped, {} failed",
                    processed_count,
                    update_result.stats.new_count,
                    update_result.stats.updated_count,
                    update_result.stats.skipped_count,
                    failed_count
                ),
                processed_count,
                failed_count,
                errors: if errors.is_empty() { None } else { Some(errors) },
            };

            // Process referral rewards for successful trading activities if SOL wallet address is
            // provided
            if let Some(sol_wallet) = &sol_wallet_address {
                let mut referral_rewards_created = 0;
                let mut referral_errors: Vec<String> = Vec::new();

                for activity in &successful_activities {
                    // Only process trading activities (perp_trade and spot_trade) for referral
                    // rewards
                    if matches!(
                        activity.activity_type,
                        crate::models::ActivityType::PerpTrade |
                            crate::models::ActivityType::SpotTrade
                    ) {
                        // Calculate actual trading volume (not notional value for perp trades)
                        let trading_volume_usd = calculate_actual_trading_volume(activity);

                        if let Some(volume) = trading_volume_usd {
                            if volume > rust_decimal::Decimal::ZERO {
                                // This is a simplified call - the actual referral processing should
                                // use
                                // process_multi_tier_trading_rewards for proper multi-tier handling
                                tracing::debug!(
                                    "Trading volume detected for referral processing: tx={}, volume=${:.2}",
                                    activity.tx_signature,
                                    volume
                                );
                                // Skip individual reward creation here as it's handled by the
                                // fallback logic
                                referral_rewards_created += 1;
                            }
                        }
                    }
                }

                if referral_rewards_created > 0 {
                    tracing::info!(
                        "Created {} referral rewards for wallet {} activities",
                        referral_rewards_created,
                        sol_wallet
                    );
                }

                if !referral_errors.is_empty() {
                    tracing::warn!(
                        "Failed to create {} referral rewards: {:?}",
                        referral_errors.len(),
                        referral_errors
                    );
                }
            }

            return Ok(Json(response));
        }
        Err(e) => {
            tracing::error!(
                "Universal update framework failed for wallet {}: {}",
                filtered_request.wallet_address,
                e
            );

            // Fallback to old logic
            let mut failed_count = 0;
            let mut errors = Vec::new();
            let mut successful_activities = Vec::new();

            for (index, activity) in deduplicated_activities_clone.iter().enumerate() {
                match state.insert_wallet_activity(activity.clone()).await {
                    Ok(_) => {
                        successful_activities.push(activity.clone());
                        tracing::debug!(
                            "Successfully stored activity {}/{}: {} for wallet {}",
                            index + 1,
                            deduplicated_activities_clone.len(),
                            activity.tx_signature,
                            request.wallet_address
                        );
                    }
                    Err(e) => {
                        failed_count += 1;
                        let error_msg = format!(
                            "Failed to store activity {}/{} ({}): {}",
                            index + 1,
                            deduplicated_activities_clone.len(),
                            activity.tx_signature,
                            e
                        );
                        errors.push(error_msg.clone());
                        tracing::error!("{}", error_msg);

                        // Log specific error types for monitoring
                        if e.to_string().contains("duplicate key") {
                            tracing::debug!(
                                "Duplicate activity detected: {}",
                                activity.tx_signature
                            );
                        } else if e.to_string().contains("connection") {
                            tracing::error!(
                                "Database connection error while storing activity: {}",
                                e
                            );
                        } else {
                            tracing::error!("Unknown database error while storing activity: {}", e);
                        }
                    }
                }
            }

            let success_count = successful_activities.len();
            let processed_count = success_count + failed_count;

            // Process referral rewards for successful trading activities if SOL wallet address is
            // provided
            if let Some(sol_wallet) = &sol_wallet_address {
                let mut referral_rewards_created = 0;
                let mut referral_errors = Vec::new();

                for activity in &successful_activities {
                    // Only process trading activities (perp_trade and spot_trade) for referral
                    // rewards
                    if matches!(
                        activity.activity_type,
                        crate::models::ActivityType::PerpTrade |
                            crate::models::ActivityType::SpotTrade
                    ) {
                        // Calculate actual trading volume (not notional value for perp trades)
                        let trading_volume_usd = calculate_actual_trading_volume(activity);

                        if let Some(trading_volume_usd) = trading_volume_usd {
                            // Get referral configuration
                            let config = crate::config::Config::get();

                            // Process multi-tier trading rewards
                            match state
                                .api_db
                                .process_multi_tier_trading_rewards(
                                    sol_wallet,
                                    trading_volume_usd,
                                    &activity.tx_signature,
                                    config.referral_tier1_percentage,
                                    config.referral_tier2_percentage,
                                    config.referral_tier3_percentage,
                                )
                                .await
                            {
                                Ok(reward_ids) => {
                                    referral_rewards_created += reward_ids.len();
                                    tracing::debug!(
                                        "Created {} referral rewards for activity: wallet={}, tx={}, volume_usd={}",
                                        reward_ids.len(),
                                        sol_wallet,
                                        activity.tx_signature,
                                        trading_volume_usd
                                    );
                                }
                                Err(e) => {
                                    let error_msg = format!(
                                        "Failed to process referral rewards for tx {}: {}",
                                        activity.tx_signature, e
                                    );
                                    referral_errors.push(error_msg.clone());
                                    tracing::error!("{}", error_msg);
                                }
                            }
                        } else {
                            tracing::debug!(
                                "No valid trading volume calculated for referral processing: tx={}",
                                activity.tx_signature
                            );
                        }
                    }
                }

                if referral_rewards_created > 0 {
                    tracing::info!(
                        "Created {} referral rewards for SOL wallet {} from {} trading activities",
                        referral_rewards_created,
                        sol_wallet,
                        successful_activities
                            .iter()
                            .filter(|a| matches!(
                                a.activity_type,
                                crate::models::ActivityType::PerpTrade |
                                    crate::models::ActivityType::SpotTrade
                            ))
                            .count()
                    );
                }

                if !referral_errors.is_empty() {
                    tracing::warn!(
                        "Encountered {} referral processing errors for SOL wallet {}: {}",
                        referral_errors.len(),
                        sol_wallet,
                        referral_errors.join("; ")
                    );
                }
            } else {
                tracing::debug!(
                    "No SOL wallet address provided in header, skipping referral rewards processing"
                );
            }

            // Return response for fallback logic
            let response = RecordActivityResponse {
                success: failed_count == 0,
                message: if failed_count == 0 {
                    format!("Successfully processed {} activities", success_count)
                } else {
                    format!(
                        "Processed {}/{} activities, {} failed",
                        success_count, processed_count, failed_count
                    )
                },
                processed_count: success_count,
                failed_count,
                errors: if errors.is_empty() { None } else { Some(errors) },
            };

            Ok(Json(response))
        }
    }
}

/// Calculate the actual trading volume for referral rewards
/// For perp trades, this should be the actual capital used, not the notional value with leverage
fn calculate_actual_trading_volume(activity: &DbWalletActivity) -> Option<rust_decimal::Decimal> {
    match activity.activity_type {
        ActivityType::PerpTrade => {
            // For perp trades, adjust for leverage if available
            if let Some(ref metadata) = activity.metadata {
                if let Some(leverage) = metadata.get("leverage").and_then(|v| v.as_f64()) {
                    if leverage > 1.0 && activity.usd_value.is_some() {
                        // Calculate actual capital used: notional_value / leverage
                        let actual_volume = activity.usd_value.unwrap() / leverage;
                        return rust_decimal::Decimal::from_f64(actual_volume);
                    }
                }
            }

            // Fallback to usd_value if no leverage info available
            // Note: This may include leverage effect, but ensures no data loss
            activity.usd_value.and_then(rust_decimal::Decimal::from_f64)
        }
        ActivityType::SpotTrade => {
            // For spot trades, the usd_value is already the actual trading volume
            activity.usd_value.and_then(rust_decimal::Decimal::from_f64)
        }
        _ => None, // Other activity types don't generate referral rewards
    }
}

/// Enhanced request validation with additional checks
/// Returns (warnings, filtered_request) where warnings contain duplicate info
fn validate_request_enhanced(
    request: &RecordActivityRequest,
) -> Result<(Vec<String>, RecordActivityRequest), String> {
    let mut warnings = Vec::new();

    // Check total activity count
    let total_fills = request.activities.user_fills.as_ref().map_or(0, |f| f.len());
    let total_updates =
        request.activities.user_non_funding_ledger_updates.as_ref().map_or(0, |u| u.len());
    let total_deposits = request.activities.deposits.as_ref().map_or(0, |d| d.len());
    let total_withdraws = request.activities.withdraws.as_ref().map_or(0, |w| w.len());
    let total_activities = total_fills + total_updates + total_deposits + total_withdraws;

    if total_activities > MAX_ACTIVITIES_PER_REQUEST {
        return Err(format!(
            "Too many activities in request: {} (max: {})",
            total_activities, MAX_ACTIVITIES_PER_REQUEST
        ));
    }

    let mut filtered_request = request.clone();

    // Filter duplicate fills and validate timestamps
    if let Some(fills) = &request.activities.user_fills {
        let mut seen_hashes = std::collections::HashSet::new();
        let mut filtered_fills = Vec::new();

        // Check for unrealistic timestamps (not too far in the future or past)
        let now = chrono::Utc::now().timestamp_millis();
        let one_year_ago = now - (365 * 24 * 60 * 60 * 1000);
        let one_hour_future = now + (60 * 60 * 1000);

        for fill in fills {
            // Check for duplicates
            if !seen_hashes.insert(&fill.hash) {
                warnings.push(format!("Duplicate fill hash detected and filtered: {}", fill.hash));
                tracing::warn!("Duplicate fill hash detected in request: {}", fill.hash);
                continue;
            }

            // Check timestamp range
            if fill.time < one_year_ago || fill.time > one_hour_future {
                return Err(format!(
                    "Fill timestamp out of reasonable range: {} (hash: {})",
                    fill.time, fill.hash
                ));
            }

            filtered_fills.push(fill.clone());
        }

        filtered_request.activities.user_fills = Some(filtered_fills);
    }

    // Filter duplicate ledger updates and validate timestamps
    if let Some(updates) = &request.activities.user_non_funding_ledger_updates {
        let mut seen_hashes = std::collections::HashSet::new();
        let mut filtered_updates = Vec::new();

        // Check timestamps for ledger updates
        let now = chrono::Utc::now().timestamp_millis();
        let one_year_ago = now - (365 * 24 * 60 * 60 * 1000);
        let one_hour_future = now + (60 * 60 * 1000);

        for update in updates {
            // Check for duplicates
            if !seen_hashes.insert(&update.hash) {
                warnings.push(format!(
                    "Duplicate ledger update hash detected and filtered: {}",
                    update.hash
                ));
                tracing::warn!("Duplicate ledger update hash detected in request: {}", update.hash);
                continue;
            }

            // Check timestamp range
            if update.time < one_year_ago || update.time > one_hour_future {
                return Err(format!(
                    "Ledger update timestamp out of reasonable range: {} (hash: {})",
                    update.time, update.hash
                ));
            }

            filtered_updates.push(update.clone());
        }

        filtered_request.activities.user_non_funding_ledger_updates = Some(filtered_updates);
    }

    // Filter duplicate deposits and validate timestamps
    if let Some(deposits) = &request.activities.deposits {
        let mut seen_hashes = std::collections::HashSet::new();
        let mut filtered_deposits = Vec::new();

        // Check timestamps for deposits
        let now = chrono::Utc::now().timestamp_millis();
        let one_year_ago = now - (365 * 24 * 60 * 60 * 1000);
        let one_hour_future = now + (60 * 60 * 1000);

        for deposit in deposits {
            // Check for duplicates
            if !seen_hashes.insert(&deposit.hash) {
                warnings.push(format!(
                    "Duplicate deposit hash detected and filtered: {}",
                    deposit.hash
                ));
                tracing::warn!("Duplicate deposit hash detected in request: {}", deposit.hash);
                continue;
            }

            // Check timestamp range
            if deposit.time < one_year_ago || deposit.time > one_hour_future {
                return Err(format!(
                    "Deposit timestamp out of reasonable range: {} (hash: {})",
                    deposit.time, deposit.hash
                ));
            }

            filtered_deposits.push(deposit.clone());
        }

        filtered_request.activities.deposits = Some(filtered_deposits);
    }

    // Filter duplicate withdraws and validate timestamps
    if let Some(withdraws) = &request.activities.withdraws {
        let mut seen_hashes = std::collections::HashSet::new();
        let mut filtered_withdraws = Vec::new();

        // Check timestamps for withdraws
        let now = chrono::Utc::now().timestamp_millis();
        let one_year_ago = now - (365 * 24 * 60 * 60 * 1000);
        let one_hour_future = now + (60 * 60 * 1000);

        for withdraw in withdraws {
            // Check for duplicates
            if !seen_hashes.insert(&withdraw.hash) {
                warnings.push(format!(
                    "Duplicate withdraw hash detected and filtered: {}",
                    withdraw.hash
                ));
                tracing::warn!("Duplicate withdraw hash detected in request: {}", withdraw.hash);
                continue;
            }

            // Check timestamp range
            if withdraw.time < one_year_ago || withdraw.time > one_hour_future {
                return Err(format!(
                    "Withdraw timestamp out of reasonable range: {} (hash: {})",
                    withdraw.time, withdraw.hash
                ));
            }

            filtered_withdraws.push(withdraw.clone());
        }

        filtered_request.activities.withdraws = Some(filtered_withdraws);
    }

    Ok((warnings, filtered_request))
}

/// Check if wallet address is authorized to submit activities
async fn check_wallet_authorization(
    state: &StorageState,
    wallet_address: &str,
) -> Result<bool, String> {
    // For now, we'll allow all wallets
    // In the future, this could check against a whitelist or rate limiting

    // Basic format validation was already done in validate_request
    // Here we could add additional checks like:
    // - Rate limiting per wallet
    // - Blacklist checking
    // - Premium user validation

    Ok(true)
}

/// Log activity statistics for monitoring
fn log_activity_statistics(
    wallet_address: &str,
    activities: &[crate::models::DbWalletActivity],
    processing_time: std::time::Duration,
) {
    let stats = HyperliquidParser::get_activity_stats(activities);

    tracing::info!(
        "Activity statistics for wallet {}: {:?}, processing_time={:?}",
        wallet_address,
        stats,
        processing_time
    );

    // Log specific metrics for monitoring systems
    for (activity_type, count) in stats {
        tracing::debug!(
            "hyperevm_activity_processed{{wallet=\"{}\", type=\"{}\"}} {}",
            wallet_address,
            activity_type,
            count
        );
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::hyperliquid_activity::{
        HyperliquidFill, HyperliquidLedgerDelta, HyperliquidNonFundingLedgerUpdate,
    };

    fn create_test_fill() -> HyperliquidFill {
        HyperliquidFill {
            coin: "BTC".to_string(),
            px: "104528.0".to_string(),
            sz: "0.00019".to_string(),
            side: "A".to_string(),
            time: 1748874639346,
            start_position: "0.00019".to_string(),
            dir: "Close Long".to_string(),
            closed_pnl: "0.02432".to_string(),
            hash: "0xa95ca3c75201de2891f80416af61da0110004a5e8453a577a3de34354530b617".to_string(),
            oid: 33045344403,
            crossed: true,
            fee: "0.008937".to_string(),
            tid: 752376183640077,
            cloid: None,
            liquidation: None,
            fee_token: "USDC".to_string(),
        }
    }

    fn create_test_ledger_update() -> HyperliquidNonFundingLedgerUpdate {
        HyperliquidNonFundingLedgerUpdate {
            time: 1749216355646,
            hash: "0xd9eb3fc868b03445e4990424f66fc101d100a1025ca8914709fa29b07a63dc27".to_string(),
            delta: HyperliquidLedgerDelta::InternalTransfer {
                usdc: "1.5".to_string(),
                user: "******************************************".to_string(),
                destination: "******************************************".to_string(),
                fee: "0.0".to_string(),
            },
        }
    }

    #[test]
    fn test_create_test_data() {
        let fill = create_test_fill();
        assert_eq!(fill.coin, "BTC");
        assert_eq!(fill.side, "A");

        let update = create_test_ledger_update();
        assert_eq!(update.time, 1749216355646);

        match &update.delta {
            HyperliquidLedgerDelta::InternalTransfer { usdc, .. } => {
                assert_eq!(usdc, "1.5");
            }
            _ => panic!("Expected InternalTransfer"),
        }
    }

    #[test]
    fn test_request_validation() {
        use crate::models::hyperliquid_activity::NestedActivities;

        // Test valid request with fills
        let request = RecordActivityRequest {
            wallet_address: "******************************************".to_string(),
            is_hyperliquid_mainnet: false,
            activities: NestedActivities {
                user_fills: Some(vec![create_test_fill()]),
                user_non_funding_ledger_updates: None,
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };
        assert!(HyperliquidParser::validate_request(&request).is_ok());

        // Test valid request with ledger updates
        let request = RecordActivityRequest {
            wallet_address: "******************************************".to_string(),
            is_hyperliquid_mainnet: false,
            activities: NestedActivities {
                user_fills: None,
                user_non_funding_ledger_updates: Some(vec![create_test_ledger_update()]),
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };
        assert!(HyperliquidParser::validate_request(&request).is_ok());

        // Test invalid wallet address
        let request = RecordActivityRequest {
            wallet_address: "invalid".to_string(),
            is_hyperliquid_mainnet: false,
            activities: NestedActivities {
                user_fills: Some(vec![create_test_fill()]),
                user_non_funding_ledger_updates: None,
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };
        assert!(HyperliquidParser::validate_request(&request).is_err());

        // Test empty request
        let request = RecordActivityRequest {
            wallet_address: "******************************************".to_string(),
            is_hyperliquid_mainnet: false,
            activities: NestedActivities {
                user_fills: None,
                user_non_funding_ledger_updates: None,
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };
        assert!(HyperliquidParser::validate_request(&request).is_err());
    }

    #[test]
    fn test_activity_parsing() {
        use crate::models::hyperliquid_activity::NestedActivities;

        let request = RecordActivityRequest {
            wallet_address: "******************************************".to_string(),
            is_hyperliquid_mainnet: false,
            activities: NestedActivities {
                user_fills: Some(vec![create_test_fill()]),
                user_non_funding_ledger_updates: Some(vec![create_test_ledger_update()]),
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };

        let activities = HyperliquidParser::parse_activities(&request).unwrap();
        assert_eq!(activities.len(), 2);

        // Check that activities have correct wallet address
        for activity in &activities {
            assert_eq!(activity.wallet_address, request.wallet_address);
            assert_eq!(activity.chain, crate::constant::CHAIN_HYPERCORE);
        }
    }

    #[test]
    fn test_duplicate_filtering_in_validation() {
        use crate::models::hyperliquid_activity::NestedActivities;

        // Create fills with duplicate hashes
        let mut fill1 = create_test_fill();
        let mut fill2 = create_test_fill();
        let mut fill3 = create_test_fill();

        fill1.hash = "0xduplicate123".to_string();
        fill2.hash = "0xduplicate123".to_string(); // Duplicate
        fill3.hash = "0xunique456".to_string();

        let request = RecordActivityRequest {
            wallet_address: "******************************************".to_string(),
            is_hyperliquid_mainnet: false,
            activities: NestedActivities {
                user_fills: Some(vec![fill1, fill2, fill3]),
                user_non_funding_ledger_updates: None,
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };

        // Test enhanced validation - should filter duplicates instead of failing
        let result = validate_request_enhanced(&request);
        assert!(result.is_ok());

        let (warnings, filtered_request) = result.unwrap();

        // Should have one warning about duplicate
        assert_eq!(warnings.len(), 1);
        assert!(warnings[0].contains("Duplicate fill hash detected and filtered: 0xduplicate123"));

        // Filtered request should only have 2 fills (duplicate removed)
        assert_eq!(filtered_request.activities.user_fills.as_ref().unwrap().len(), 2);

        // Check that the remaining fills have unique hashes
        let remaining_hashes: Vec<&String> = filtered_request
            .activities
            .user_fills
            .as_ref()
            .unwrap()
            .iter()
            .map(|f| &f.hash)
            .collect();
        assert!(remaining_hashes.contains(&&"0xduplicate123".to_string()));
        assert!(remaining_hashes.contains(&&"0xunique456".to_string()));
    }

    #[test]
    fn test_duplicate_filtering_with_ledger_updates() {
        use crate::models::hyperliquid_activity::NestedActivities;

        // Create ledger updates with duplicate hashes
        let mut update1 = create_test_ledger_update();
        let mut update2 = create_test_ledger_update();

        update1.hash = "0xduplicate789".to_string();
        update2.hash = "0xduplicate789".to_string(); // Duplicate

        let request = RecordActivityRequest {
            wallet_address: "******************************************".to_string(),
            is_hyperliquid_mainnet: false,
            activities: NestedActivities {
                user_fills: None,
                user_non_funding_ledger_updates: Some(vec![update1, update2]),
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };

        let result = validate_request_enhanced(&request);
        assert!(result.is_ok());

        let (warnings, filtered_request) = result.unwrap();

        // Should have one warning about duplicate
        assert_eq!(warnings.len(), 1);
        assert!(warnings[0]
            .contains("Duplicate ledger update hash detected and filtered: 0xduplicate789"));

        // Filtered request should only have 1 update (duplicate removed)
        assert_eq!(
            filtered_request.activities.user_non_funding_ledger_updates.as_ref().unwrap().len(),
            1
        );
    }

    #[test]
    fn test_calculate_actual_trading_volume_perp_with_leverage() {
        use crate::constant::CHAIN_HYPEREVM;
        use rust_decimal::Decimal;

        // Test perp trade with leverage in metadata
        let activity = DbWalletActivity {
            wallet_address: "test".to_string(),
            tx_signature: "test".to_string(),
            activity_type: ActivityType::PerpTrade,
            token_mint: Some("SOL".to_string()),
            token_decimals: Some(9),
            token_amount: Some(1000000000),
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value: Some(1000.0), // $1000 notional value
            timestamp: 1234567890,
            block_time: Some(1234567890),
            slot: None,
            chain: CHAIN_HYPEREVM,
            metadata: Some(serde_json::json!({
                "leverage": 10.0
            })),
            created_at: 1234567890,
        };

        let result = calculate_actual_trading_volume(&activity);
        assert!(result.is_some());

        // With 10x leverage, actual capital should be $1000 / 10 = $100
        let expected = Decimal::from_f64(100.0).unwrap();
        assert_eq!(result.unwrap(), expected);
    }

    #[test]
    fn test_calculate_actual_trading_volume_spot_trade() {
        use crate::constant::CHAIN_HYPEREVM;
        use rust_decimal::Decimal;

        // Test spot trade (no leverage)
        let activity = DbWalletActivity {
            wallet_address: "test".to_string(),
            tx_signature: "test".to_string(),
            activity_type: ActivityType::SpotTrade,
            token_mint: Some("SOL".to_string()),
            token_decimals: Some(9),
            token_amount: Some(1000000000),
            base_mint: None,
            base_decimals: None,
            base_amount: None,
            usd_value: Some(500.0),
            timestamp: 1234567890,
            block_time: Some(1234567890),
            slot: None,
            chain: CHAIN_HYPEREVM,
            metadata: Some(serde_json::json!({})),
            created_at: 1234567890,
        };

        let result = calculate_actual_trading_volume(&activity);
        assert!(result.is_some());

        // For spot trades, usd_value is the actual trading volume
        let expected = Decimal::from_f64(500.0).unwrap();
        assert_eq!(result.unwrap(), expected);
    }
}
